diff --git a/pom.xml b/pom.xml
index f69ecb7..dd5e56d 100644
--- a/pom.xml
+++ b/pom.xml
@@ -1,218 +1,224 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
     <modelVersion>4.0.0</modelVersion>
     <parent>
         <groupId>org.springframework.boot</groupId>
         <artifactId>spring-boot-starter-parent</artifactId>
         <version>3.4.5</version>
         <relativePath/> <!-- lookup parent from repository -->
     </parent>
     <groupId>com.iflytek.traffic</groupId>
     <artifactId>traffic-server</artifactId>
     <version>0.0.1-SNAPSHOT</version>
     <name>traffic-server</name>
     <description>Traffic server</description>
 
     <properties>
         <java.version>17</java.version>
         <protobuf-java.version>3.25.7</protobuf-java.version>
         <alibaba.cloud.version>2022.0.0.0</alibaba.cloud.version>
     </properties>
 
     <dependencies>
         <dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-web</artifactId>
             <exclusions>
                 <exclusion>
                     <groupId>org.springframework.boot</groupId>
                     <artifactId>spring-boot-starter-logging</artifactId>
                 </exclusion>
             </exclusions>
         </dependency>
 
         <dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-actuator</artifactId>
         </dependency>
 
         <dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-log4j2</artifactId>
         </dependency>
 
         <dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-data-redis</artifactId>
         </dependency>
 
         <dependency>
             <groupId>io.micrometer</groupId>
             <artifactId>micrometer-registry-prometheus</artifactId>
             <version>1.9.6</version>
         </dependency>
 
         <dependency>
             <groupId>cn.hutool</groupId>
             <artifactId>hutool-all</artifactId>
             <version>5.8.25</version>
         </dependency>
 
         <dependency>
             <groupId>com.google.guava</groupId>
             <artifactId>guava</artifactId>
             <version>33.2.1-jre</version>
         </dependency>
 
         <dependency>
             <groupId>io.vertx</groupId>
             <artifactId>vertx-core</artifactId>
             <version>4.5.9</version>
         </dependency>
 
         <dependency>
             <groupId>io.vertx</groupId>
             <artifactId>vertx-micrometer-metrics</artifactId>
             <version>4.5.9</version>
         </dependency>
 
         <dependency>
             <groupId>com.github.ben-manes.caffeine</groupId>
             <artifactId>caffeine</artifactId>
             <version>3.1.8</version>
         </dependency>
 
         <dependency>
             <groupId>org.projectlombok</groupId>
             <artifactId>lombok</artifactId>
             <optional>true</optional>
         </dependency>
         <dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-starter-test</artifactId>
             <scope>test</scope>
         </dependency>
         <dependency>
             <groupId>com.alibaba</groupId>
             <artifactId>fastjson</artifactId>
             <version>1.2.83</version>
         </dependency>
         <!--mysql相关-->
         <dependency>
             <groupId>org.mybatis.spring.boot</groupId>
             <artifactId>mybatis-spring-boot-starter</artifactId>
             <version>3.0.3</version>
         </dependency>
         <dependency>
             <groupId>mysql</groupId>
             <artifactId>mysql-connector-java</artifactId>
             <version>8.0.17</version>
         </dependency>
 
         <dependency>
             <groupId>org.apache.commons</groupId>
             <artifactId>commons-lang3</artifactId>
             <version>3.17.0</version>
         </dependency>
 
         <dependency>
             <groupId>org.apache.commons</groupId>
             <artifactId>commons-collections4</artifactId>
             <version>4.4</version>
         </dependency>
 
         <dependency>
             <groupId>com.google.protobuf</groupId>
             <artifactId>protobuf-java</artifactId>
             <version>${protobuf-java.version}</version>
         </dependency>
         <dependency>
             <groupId>com.ip2location</groupId>
             <artifactId>ip2location-java</artifactId>
             <version>8.12.0</version>
         </dependency>
 
         <dependency>
             <groupId>com.google.protobuf</groupId>
             <artifactId>protobuf-java-util</artifactId>
             <version>${protobuf-java.version}</version>
         </dependency>
 
         <dependency>
             <groupId>com.alibaba.cloud</groupId>
             <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
             <version>${alibaba.cloud.version}</version>
 <!--            <exclusions>-->
 <!--                <exclusion>-->
 <!--                    <artifactId>nacos-client</artifactId>-->
 <!--                    <groupId>com.alibaba.nacos</groupId>-->
 <!--                </exclusion>-->
 <!--            </exclusions>-->
         </dependency>
 
         <dependency>
             <groupId>com.alibaba.cloud</groupId>
             <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
             <version>${alibaba.cloud.version}</version>
         </dependency>
 
+        <dependency>
+            <groupId>io.dropwizard.metrics</groupId>
+            <artifactId>metrics-core</artifactId>
+            <version>4.2.17</version>
+        </dependency>
+
     </dependencies>
 
     <build>
         <extensions>
             <extension>
                 <groupId>kr.motd.maven</groupId>
                 <artifactId>os-maven-plugin</artifactId>
                 <version>1.7.1</version>
             </extension>
         </extensions>
         <plugins>
             <!--            <plugin>-->
             <!--                <groupId>org.xolstice.maven.plugins</groupId>-->
             <!--                <artifactId>protobuf-maven-plugin</artifactId>-->
             <!--                <version>0.6.1</version>-->
             <!--                <executions>-->
             <!--                    <execution>-->
             <!--                        <goals>-->
             <!--                            <goal>compile</goal>-->
             <!--                            <goal>test-compile</goal>-->
             <!--                        </goals>-->
             <!--                        <configuration>-->
             <!--                            &lt;!&ndash;suppress UnresolvedMavenProperty &ndash;&gt;-->
             <!--                            <protocArtifact>-->
             <!--                                com.google.protobuf:protoc:${protobuf-java.version}:exe:${os.detected.classifier}-->
             <!--                            </protocArtifact>-->
             <!--                        </configuration>-->
             <!--                    </execution>-->
             <!--                </executions>-->
             <!--            </plugin>-->
             <plugin>
                 <groupId>org.apache.maven.plugins</groupId>
                 <artifactId>maven-compiler-plugin</artifactId>
                 <configuration>
                     <annotationProcessorPaths>
                         <path>
                             <groupId>org.projectlombok</groupId>
                             <artifactId>lombok</artifactId>
                         </path>
                     </annotationProcessorPaths>
                 </configuration>
             </plugin>
             <plugin>
                 <groupId>org.springframework.boot</groupId>
                 <artifactId>spring-boot-maven-plugin</artifactId>
                 <configuration>
                     <excludes>
                         <exclude>
                             <groupId>org.projectlombok</groupId>
                             <artifactId>lombok</artifactId>
                         </exclude>
                     </excludes>
                 </configuration>
             </plugin>
         </plugins>
         <finalName>traffic-server</finalName>
     </build>
 
 </project>
diff --git a/prod-config/eu/log4j2.xml b/prod-config/eu/log4j2.xml
index d12f0b2..739b44e 100644
--- a/prod-config/eu/log4j2.xml
+++ b/prod-config/eu/log4j2.xml
@@ -1,140 +1,140 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <Configuration status="OFF" monitorinterval="5">
     <Properties>
         <property name="log_pattern_default">%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%t] [%logger] - %msg%n</property>
         <property name="log_dir_default">logs</property>
         <property name="log_pattern_event">%d{yyyy-MM-dd-HH:mm:ss.SSS} %msg%n</property>
     </Properties>
 
     <Appenders>
         <Console name="Console" target="SYSTEM_OUT">
             <PatternLayout pattern="${log_pattern_default}" disableAnsi="false" noConsoleNoAnsi="false"/>
         </Console>
 
         <RollingRandomAccessFile name="DefaultAppender" fileName="${log_dir_default}/traffic-server.log"
                                  filePattern="${log_dir_default}/archive/%d{yyyy-MM-dd}/traffic-server%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true"
                                  append="true">
             <PatternLayout pattern="${log_pattern_default}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="500MB"/>
             </Policies>
             <DefaultRolloverStrategy max="128">
                 <Delete basePath="${log_dir_default}/archive" maxDepth="2">
                     <IfFileName glob="*/traffic-server*.log"/>
-                    <IfLastModified age="60d"/>
+                    <IfLastModified age="30d"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
         <RollingRandomAccessFile name="AbroadAdxTraceAppender"
                                  fileName="${log_dir_default}/trace/trace.log"
                                  filePattern="${log_dir_default}/trace/trace_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/trace" maxDepth="2">
                     <IfFileName glob="trace*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
         <RollingRandomAccessFile name="AbroadAdxNoBidAppender"
                                  fileName="${log_dir_default}/no_bid/no_bid.log"
                                  filePattern="${log_dir_default}/no_bid/no_bid_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/no_bid" maxDepth="2">
                     <IfFileName glob="no_bid*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="MaterialLogAppender"
                                  fileName="${log_dir_default}/material/material.log"
                                  filePattern="${log_dir_default}/material/material_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/material" maxDepth="2">
                     <IfFileName glob="material*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AdmVerifyLogAppender"
                                  fileName="${log_dir_default}/adm_verify/adm_verify.log"
                                  filePattern="${log_dir_default}/adm_verify/adm_verify_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/adm_verify" maxDepth="2">
                     <IfFileName glob="adm_verify*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AbroadSessionSampleAppender"
                                  fileName="${log_dir_default}/sample/sample.log"
                                  filePattern="${log_dir_default}/sample/sample_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/sample" maxDepth="2">
                     <IfFileName glob="sample*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
     </Appenders>
     <Loggers>
         <Root level="WARN">
             <AppenderRef ref="DefaultAppender"/>
         </Root>
 
         <logger name="abroad_adx_trace_log" level="DEBUG" additivity="false">
             <AppenderRef ref="AbroadAdxTraceAppender"/>
         </logger>
         <logger name="abroad_adx_no_bid_log" level="DEBUG" additivity="false">
             <AppenderRef ref="AbroadAdxNoBidAppender"/>
         </logger>
 
         <logger name="material_log" level="DEBUG" additivity="false">
             <AppenderRef ref="MaterialLogAppender"/>
         </logger>
 
         <logger name="adm_verify_log" level="INFO" additivity="false">
             <AppenderRef ref="AdmVerifyLogAppender"/>
         </logger>
 
         <logger name="abroad_session_sample_log" level="INFO" additivity="false">
             <AppenderRef ref="AbroadSessionSampleAppender"/>
         </logger>
 
     </Loggers>
 </Configuration>
\ No newline at end of file
diff --git a/prod-config/sg/log4j2.xml b/prod-config/sg/log4j2.xml
index a731913..53805e0 100644
--- a/prod-config/sg/log4j2.xml
+++ b/prod-config/sg/log4j2.xml
@@ -1,182 +1,182 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <Configuration status="OFF" monitorinterval="5">
     <Properties>
         <property name="log_pattern_default">%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%t] [%logger] - %msg%n</property>
         <property name="log_dir_default">logs</property>
         <property name="log_pattern_event">%d{yyyy-MM-dd-HH:mm:ss.SSS} %msg%n</property>
     </Properties>
 
     <Appenders>
         <Console name="Console" target="SYSTEM_OUT">
             <PatternLayout pattern="${log_pattern_default}" disableAnsi="false" noConsoleNoAnsi="false"/>
         </Console>
 
         <RollingRandomAccessFile name="DefaultAppender" fileName="${log_dir_default}/traffic-server.log"
                                  filePattern="${log_dir_default}/archive/%d{yyyy-MM-dd}/traffic-server%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true"
                                  append="true">
             <PatternLayout pattern="${log_pattern_default}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="500MB"/>
             </Policies>
             <DefaultRolloverStrategy max="128">
                 <Delete basePath="${log_dir_default}/archive" maxDepth="2">
                     <IfFileName glob="*/traffic-server*.log"/>
-                    <IfLastModified age="60d"/>
+                    <IfLastModified age="30d"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
         <RollingRandomAccessFile name="AbroadAdxTraceAppender"
                                  fileName="${log_dir_default}/trace/trace.log"
                                  filePattern="${log_dir_default}/trace/trace_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/trace" maxDepth="2">
                     <IfFileName glob="trace*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
         <RollingRandomAccessFile name="AbroadAdxNoBidAppender"
                                  fileName="${log_dir_default}/no_bid/no_bid.log"
                                  filePattern="${log_dir_default}/no_bid/no_bid_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/no_bid" maxDepth="2">
                     <IfFileName glob="no_bid*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="FilebeatAbroadAdxTraceAppender"
                                  fileName="${log_dir_default}/filebeat_trace/filebeat_trace.log"
                                  filePattern="${log_dir_default}/filebeat_trace/filebeat_trace_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/filebeat_trace" maxDepth="2">
                     <IfFileName glob="filebeat_trace*.log"/>
                     <IfLastModified age="1h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="MaterialLogAppender"
                                  fileName="${log_dir_default}/material/material.log"
                                  filePattern="${log_dir_default}/material/material_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/material" maxDepth="2">
                     <IfFileName glob="material*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AdmVerifyLogAppender"
                                  fileName="${log_dir_default}/adm_verify/adm_verify.log"
                                  filePattern="${log_dir_default}/adm_verify/adm_verify_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/adm_verify" maxDepth="2">
                     <IfFileName glob="adm_verify*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AbroadSessionSampleAppender"
                                  fileName="${log_dir_default}/sample/sample.log"
                                  filePattern="${log_dir_default}/sample/sample_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/sample" maxDepth="2">
                     <IfFileName glob="sample*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AbroadAdxRetrievalAppender"
                                  fileName="${log_dir_default}/retrieval/retrieval.log"
                                  filePattern="${log_dir_default}/retrieval/retrieval_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/retrieval" maxDepth="2">
                     <IfFileName glob="retrieval*.log"/>
                     <IfLastModified age="1h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
     </Appenders>
     <Loggers>
         <Root level="WARN">
             <AppenderRef ref="DefaultAppender"/>
         </Root>
 
         <logger name="abroad_adx_trace_log" level="DEBUG" additivity="false">
             <AppenderRef ref="AbroadAdxTraceAppender"/>
         </logger>
         <logger name="abroad_adx_no_bid_log" level="DEBUG" additivity="false">
             <AppenderRef ref="AbroadAdxNoBidAppender"/>
         </logger>
 
         <logger name="material_log" level="DEBUG" additivity="false">
             <AppenderRef ref="MaterialLogAppender"/>
         </logger>
 
         <logger name="adm_verify_log" level="INFO" additivity="false">
             <AppenderRef ref="AdmVerifyLogAppender"/>
         </logger>
 
         <logger name="filebeat_abroad_adx_trace_log" level="DEBUG" additivity="false">
             <AppenderRef ref="FilebeatAbroadAdxTraceAppender"/>
         </logger>
 
         <logger name="abroad_session_sample_log" level="INFO" additivity="false">
             <AppenderRef ref="AbroadSessionSampleAppender"/>
         </logger>
 
         <logger name="abroad_adx_retrieval_log" level="INFO" additivity="false">
             <AppenderRef ref="AbroadAdxRetrievalAppender"/>
         </logger>
 
     </Loggers>
 </Configuration>
\ No newline at end of file
diff --git a/prod-config/us/log4j2.xml b/prod-config/us/log4j2.xml
index d12f0b2..739b44e 100644
--- a/prod-config/us/log4j2.xml
+++ b/prod-config/us/log4j2.xml
@@ -1,140 +1,140 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <Configuration status="OFF" monitorinterval="5">
     <Properties>
         <property name="log_pattern_default">%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%t] [%logger] - %msg%n</property>
         <property name="log_dir_default">logs</property>
         <property name="log_pattern_event">%d{yyyy-MM-dd-HH:mm:ss.SSS} %msg%n</property>
     </Properties>
 
     <Appenders>
         <Console name="Console" target="SYSTEM_OUT">
             <PatternLayout pattern="${log_pattern_default}" disableAnsi="false" noConsoleNoAnsi="false"/>
         </Console>
 
         <RollingRandomAccessFile name="DefaultAppender" fileName="${log_dir_default}/traffic-server.log"
                                  filePattern="${log_dir_default}/archive/%d{yyyy-MM-dd}/traffic-server%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true"
                                  append="true">
             <PatternLayout pattern="${log_pattern_default}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="500MB"/>
             </Policies>
             <DefaultRolloverStrategy max="128">
                 <Delete basePath="${log_dir_default}/archive" maxDepth="2">
                     <IfFileName glob="*/traffic-server*.log"/>
-                    <IfLastModified age="60d"/>
+                    <IfLastModified age="30d"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
         <RollingRandomAccessFile name="AbroadAdxTraceAppender"
                                  fileName="${log_dir_default}/trace/trace.log"
                                  filePattern="${log_dir_default}/trace/trace_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/trace" maxDepth="2">
                     <IfFileName glob="trace*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
         <RollingRandomAccessFile name="AbroadAdxNoBidAppender"
                                  fileName="${log_dir_default}/no_bid/no_bid.log"
                                  filePattern="${log_dir_default}/no_bid/no_bid_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/no_bid" maxDepth="2">
                     <IfFileName glob="no_bid*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="MaterialLogAppender"
                                  fileName="${log_dir_default}/material/material.log"
                                  filePattern="${log_dir_default}/material/material_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/material" maxDepth="2">
                     <IfFileName glob="material*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AdmVerifyLogAppender"
                                  fileName="${log_dir_default}/adm_verify/adm_verify.log"
                                  filePattern="${log_dir_default}/adm_verify/adm_verify_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/adm_verify" maxDepth="2">
                     <IfFileName glob="adm_verify*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
         <RollingRandomAccessFile name="AbroadSessionSampleAppender"
                                  fileName="${log_dir_default}/sample/sample.log"
                                  filePattern="${log_dir_default}/sample/sample_%d{yyyy-MM-dd-HH}_%i.log"
                                  immediateFlush="true" append="true" ignoreExceptions="false">
             <PatternLayout pattern="${log_pattern_event}"/>
             <Policies>
                 <TimeBasedTriggeringPolicy/>
                 <SizeBasedTriggeringPolicy size="4096MB"/>
             </Policies>
             <DefaultRolloverStrategy max="256">
                 <Delete basePath="${log_dir_default}/sample" maxDepth="2">
                     <IfFileName glob="sample*.log"/>
                     <IfLastModified age="4h"/>
                 </Delete>
             </DefaultRolloverStrategy>
         </RollingRandomAccessFile>
 
     </Appenders>
     <Loggers>
         <Root level="WARN">
             <AppenderRef ref="DefaultAppender"/>
         </Root>
 
         <logger name="abroad_adx_trace_log" level="DEBUG" additivity="false">
             <AppenderRef ref="AbroadAdxTraceAppender"/>
         </logger>
         <logger name="abroad_adx_no_bid_log" level="DEBUG" additivity="false">
             <AppenderRef ref="AbroadAdxNoBidAppender"/>
         </logger>
 
         <logger name="material_log" level="DEBUG" additivity="false">
             <AppenderRef ref="MaterialLogAppender"/>
         </logger>
 
         <logger name="adm_verify_log" level="INFO" additivity="false">
             <AppenderRef ref="AdmVerifyLogAppender"/>
         </logger>
 
         <logger name="abroad_session_sample_log" level="INFO" additivity="false">
             <AppenderRef ref="AbroadSessionSampleAppender"/>
         </logger>
 
     </Loggers>
 </Configuration>
\ No newline at end of file
diff --git a/src/main/java/com/iflytek/traffic/controller/DataTestController.java b/src/main/java/com/iflytek/traffic/controller/DataTestController.java
index 6b75e97..7309497 100644
--- a/src/main/java/com/iflytek/traffic/controller/DataTestController.java
+++ b/src/main/java/com/iflytek/traffic/controller/DataTestController.java
@@ -1,78 +1,104 @@
 package com.iflytek.traffic.controller;
 
+import com.iflytek.traffic.data.provider.DspEpQpsProvider;
 import com.iflytek.traffic.data.provider.InjectProvider;
-import com.iflytek.traffic.data.provider.SspDataProvider;
 import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.web.bind.annotation.GetMapping;
 import org.springframework.web.bind.annotation.RequestMapping;
 import org.springframework.web.bind.annotation.RestController;
 
+import java.util.Map;
+
 @RestController
 @RequestMapping("/dataTest")
 public class DataTestController {
 
-    public static String testInject(){
+    @Autowired
+    private DspEpQpsProvider dspEpQpsProvider;
+
+    public static String testInject() {
         InjectProvider injectProvider = new InjectProvider();
         //String adm = "{\"native\":{\"assets\":[{\"id\":1,\"required\":1,\"title\":{\"text\":\"สวัสดีค่ะ สินค้าที่คุณชอบลดราคา\"}},{\"data\":{\"value\":\"อย่าพลาดโอกาส\"},\"id\":5,\"required\":0},{\"id\":2,\"img\":{\"h\":180,\"url\":\"https://material.growone.sg/online/assets/2025/03/25/67932c9936cfc2b7e5130c1c5a84dfa7.jpg\",\"w\":180},\"required\":1},{\"id\":3,\"img\":{\"h\":627,\"url\":\"https://material.growone.sg/online/assets/2025/04/10/50b088e5bd0ce3c0f042dfcbaff41951.jpg\",\"w\":1200},\"required\":1}],\"imptrackers\":[\"https://monitor.growone.sg/vw?info=CIeBkcEGEJ0BGiQ4Y2NhZjVkMi01YzlmLTRlMjQtOTRjYy05ODgxYzc3MTQ0ZTMiATAokU4wq05AvQhIjzVQ-0FY8TNgj5MFciA3OEMyMjJFNUQyQzYxRDQzMkY1RjQxNzAwQzBDNUQ2Qn0AAAAAhQEAAAAAiAHohAGQAQKaASQ5MWY0ZjVlMS0zZmM0LTRmODEtYjFkZS1hODJlMWU2MWExYmGgAcDRAqgBANABAtgBAuABAfABuO262wH6AQEyggIBMpICBDIwMTWiAgkxLjMxNi42LjGqAjZjb20ubWl1aS5tc2EuZ2xvYmFsfDExNHwwfDB8ODY3MCwwLDAmJiYwLDAsMCwwLDB8MHx8VEiwAlC4AoCwy4-eFuACAegCB_ACB_gCB4IDCQjeQxACGOmlBoIDBAgAEAKCAxQIABACGMWcARj2pQYY_5wBGL_qAYIDBAgAEAGCAwQIABABggMECAAQAYIDBAgAEAGCAwQIABABiAMAqAPYBA&wp=${AUCTION_PRICE}\"],\"link\":{\"clicktrackers\":[\"https://monitor.growone.sg/ck?info=CIeBkcEGEJ0BGiQ4Y2NhZjVkMi01YzlmLTRlMjQtOTRjYy05ODgxYzc3MTQ0ZTMiATAokU4wq05AvQhIjzVQ-0FY8TNgj5MFciA3OEMyMjJFNUQyQzYxRDQzMkY1RjQxNzAwQzBDNUQ2Qn0AAAAAhQEAAAAAiAHohAGQAQKaASQ5MWY0ZjVlMS0zZmM0LTRmODEtYjFkZS1hODJlMWU2MWExYmGgAcDRAqgBANABAtgBAuABAfABuO262wH6AQEyggIBMpICBDIwMTWiAgkxLjMxNi42LjGqAjZjb20ubWl1aS5tc2EuZ2xvYmFsfDExNHwwfDB8ODY3MCwwLDAmJiYwLDAsMCwwLDB8MHx8VEiwAlC4AoCwy4-eFuACAegCB_ACB_gCB4IDCQjeQxACGOmlBoIDBAgAEAKCAxQIABACGMWcARj2pQYY_5wBGL_qAYIDBAgAEAGCAwQIABABggMECAAQAYIDBAgAEAGCAwQIABABiAMAqAPYBA\"],\"url\":\"https://c.lazada.co.th/t/c.11PvH0?sub_id1=6799&sub_aff_id=157&sub_id6=b8d10a31-75cf-4dac-84b9-59f622544459&sub_id3=78C222E5D2C61D432F5F41700C0C5D6B&sub_id2=8ccaf5d2-5c9f-4e24-94cc-9881c77144e3&sub_id5=CIeBkcEGEJ0BGiQ4Y2NhZjVkMi01YzlmLTRlMjQtOTRjYy05ODgxYzc3MTQ0ZTMiATAokU4wq05AvQhIjzVQ-0FY8TNgj5MFciA3OEMyMjJFNUQyQzYxRDQzMkY1RjQxNzAwQzBDNUQ2Qn0AAAAAhQEAAAAAiAHohAGQAQKaASQ5MWY0ZjVlMS0zZmM0LTRmODEtYjFkZS1hODJlMWU2MWExYmGgAcDRAqgBANABAtgBAuABAfABuO262wH6AQEyggIBMpICBDIwMTWiAgkxLjMxNi42LjGqAjZjb20ubWl1aS5tc2EuZ2xvYmFsfDExNHwwfDB8ODY3MCwwLDAmJiYwLDAsMCwwLDB8MHx8VEiwAlC4AoCwy4-eFuACAegCB_ACB_gCB4IDCQjeQxACGOmlBoIDBAgAEAKCAxQIABACGMWcARj2pQYY_5wBGL_qAYIDBAgAEAGCAwQIABABggMECAAQAYIDBAgAEAGCAwQIABABiAMAqAPYBA&sub_id4=2015\"}}}";
         //String viewLink = "https://test.growone.sg/test?info=testtsettesttset";
         //int type = 3;
         String adm = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                 "<VAST version=\"3.0\">\n" +
                 "  <Ad id=\"14042\">\n" +
                 "    <InLine>\n" +
                 "      <AdSystem><![CDATA[TaurusX]]></AdSystem>\n" +
                 "      <AdTitle><![CDATA[SHEIN-Shopping Online]]></AdTitle>\n" +
                 "      <Impression><![CDATA[https://panda.clickhubs.com/impression?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&su=1&ei=f]]></Impression>\n" +
                 "      <Error><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=error]]></Error>\n" +
                 "      <Impression><![CDATA[https://rtbtrack-asia.rtblab.net/imp?p=550&d=182&t=dmpNKcXFwX8T5oy34Ktpf5u-AYlO7uEksbPI-4OvNfAQ8C-Wv8ljXLFdr0kvQejPKRdCrgFEScpouANK2vTEkYS6xHkP0fXBc6yAhGJq7d4WduyqRdon_cKtaZFxJnlDD8YbF8-WDuVTIYji4_qRjMuflJt6I9tTsHBiZAFEstdRw4L-o0aCEensmxLrIz9u7K2uR7QigQ8XoakEl5N-wl-KsjzsAlFBHwl-3EgSaiPJEkehhOlfXW-I8iCM8guXuXkkncFc1dV6gyenU7NqFoAsIm4g9kTn014UnR4qT81oGK6MWoHeRYH7Sxesw3_9N0TTmaJb0JJuVNIJW_Xx2SUo72fZMn1qUCwLMHBlv_fBTnNmlVkfydVQgmFBYW1nlzYvHKfsoXTt_jA0j8Ed0IkYWSNfBDoXGOlxmdC9d77QoE5e4otIQUtfHTn_pnloRhYU6jbwmdOcaOKTrcJtr7C2ig7zVxPL0MK6-drwSM88Gq7oGz4vjYrfblWmZUEGiEts0T_cywCRjAPPqfn33JrYb0TnsAqpiMud7Q4_c9jugPUqs4M26Y3OlBTUZ5lK&ns=OKJ1Fnh7MmgQ9BgOUgnwX0pgW17cHXI2ronUKheRSHmxmawDftIn98pcynvYms5Ff4yKSUK5HXddVyvhZ5_yF2cmq-m-cUYjAlZl3Fi-_rAcp6m6B4Ywg3AVjNgbTze9Mc8P5Gligdx6RbLfYiXhZzYp8N-2I3LY4F9MUX-II04KyVQWe4TtPIr07XlBcVGParum7q8b4Mw0bJZ8x2cbeQqSMkE0oT-hJ18cgzadhcW305LNxhdNMmxqiTe6V0WW5b348a9LnT-RzlNNyfDlsqQkRXrTJuRRApNtXk8n7ddPyQQ1P88yCaZgduezU_89akvmw5njqq5prNQ0Sg0nYmYAt_NyzMKEHY2MCFI6-vKnbofSqJZ22HGTbCO0j9AiybVTKJoxsDgeQ1xDortU5vHoKE06JfuKVZc-JTX7oG1sMEIaiPb1vmlFF038UgovmajAywx4kmeVk6WeDZqujDi9S-jA993IHDSwGn8l-yLoCz7JF0UIZ-d6KiiIAtuyyFQ2likM7RobVzXKPFiSH5s16x22rKHmFMSjZmH9YOiv176tir0SqNengUS4G4HRqk7wfMTLot6GtFAPFh8Gtjp9qh1p4YD293r6omi2sY-pgrFuVlgk4n9uv1B3Wh5xjynu-M0kOG8W6erUQy5OoEDRHVgPN4-oWk-8kaiSE8hf_EPHoKIH9h4WIN_JXWILLaJUDf8f5TnzT10LKCoznRwasaLrqqogD3OTTTXk3UOGmTtxpkBjOijWtYRU2DoAYj00t2mfS9MZUAbFu5LzEfzgRVGljv1yfoOgSqQbXDJTQA_W1IhlS7pthmo5Klhbxk_UuSVbn7xSDD7HbPnuuEcu5jg83eLVa85y3qN_ukawtPvJkNLrz7nF9CcfPSQQ47aAnY7BCeSN8bF4IBcjdQ&tx=]]></Impression>\n" +
                 "      <Error><![CDATA[https://rtbtrack-asia.rtblab.net/vasterror?p=550&d=182&c=46930&b=00040b17f8631981ff41_2480&error=[ERRORCODE]]]></Error>\n" +
                 "      <Creatives>\n" +
                 "        <Creative id=\"46930\" adId=\"14042\">\n" +
                 "          <Linear>\n" +
                 "            <Duration>00:00:15</Duration>\n" +
                 "            <MediaFiles>\n" +
                 "              <MediaFile type=\"video/mp4\" width=\"720\" height=\"1280\" bitrate=\"1596\"\n" +
                 "                delivery=\"progressive\" maintainAspectRatio=\"true\" scalable=\"true\"><![CDATA[https://dsp-static.clickhubs.com/46930/0925_my_720_wyl_fcy.mp4]]></MediaFile>\n" +
                 "            </MediaFiles>\n" +
                 "            <VideoClicks>\n" +
                 "              <ClickThrough><![CDATA[https://panda.clickhubs.com/fall/back?bundle=com.zzkko&clickid=xrktksop9wumnssnruqlmlvlpvsrtopsiljlnkkl9kliojmniqnk]]></ClickThrough>\n" +
                 "              <ClickTracking><![CDATA[https://app.appsflyer.com/com.zzkko?af_channel=&af_sub1=C_140420&af_sub2=5034&af_sub3=741&redirect=false&c=SHEIN-TaurusX-APPRMT-newlogic-MY-ANDROID-ALL-RTA-D&af_adset_id=1000568&af_reengagement_window=1d&af_click_lookback=1d&advertising_id=627ff7bd-c968-40e5-999c-c8b25862a9f4&idfa=&af_ip=**************&pid=taurus_int&af_cost_model=cpa&is_retargeting=true&af_ua=Mozilla%2F5.0+%28Linux%3B+Android+14%3B+SM-S908E+Build%2FUP1A.231005.007%3B+wv%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Version%2F4.0+Chrome%2F130.0.6723.108+Mobile+Safari%2F537.36&af_adset=shein_taurusx_android_my_rmt_RTA_D_Other_20240621_tagid_1960_mixcatid_351&af_c_id=100271&clickid=xrktksop9wumnssnruqlmlvlpvsrtopsiljlnkkl9kliojmniqnk&af_cost_value=7.2&af_siteid=zQdi-OCGalhuOuGKYHxb2U4RCMAix7YHYEaYuqoBgBbesarXFqtawny7Xd_IHgwX&af_ad_id=10007148&af_lang=en&af_ad=my_Other_video_9x16_20240621B_0001]]></ClickTracking>\n" +
                 "              <ClickTracking><![CDATA[https://panda.clickhubs.com/click?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034]]></ClickTracking>\n" +
                 "              <ClickTracking><![CDATA[https://rtbtrack-asia.rtblab.net/clk?p=550&d=182&t=dmpNKcXFwX8T5oy34Ktpf5u-AYlO7uEksbPI-4OvNfAQ8C-Wv8ljXLFdr0kvQejPKRdCrgFEScpouANK2vTEkYS6xHkP0fXBc6yAhGJq7d4WduyqRdon_cKtaZFxJnlDD8YbF8-WDuVTIYji4_qRjMuflJt6I9tTsHBiZAFEstdRw4L-o0aCEensmxLrIz9u7K2uR7QigQ8XoakEl5N-wl-KsjzsAlFBHwl-3EgSaiPJEkehhOlfXW-I8iCM8guXuXkkncFc1dV6gyenU7NqFoAsIm4g9kTn014UnR4qT81oGK6MWoHeRYH7Sxesw3_9N0TTmaJb0JJuVNIJW_Xx2SUo72fZMn1qUCwLMHBlv_fBTnNmlVkfydVQgmFBYW1nlzYvHKfsoXTt_jA0j8Ed0IkYWSNfBDoXGOlxmdC9d77QoE5e4otIQUtfHTn_pnloRhYU6jbwmdOcaOKTrcJtr7C2ig7zVxPL0MK6-drwSM88Gq7oGz4vjYrfblWmZUEGiEts0T_cywCRjAPPqfn33JrYb0TnsAqpiMud7Q4_c9jugPUqs4M26Y3OlBTUZ5lK&tx=]]></ClickTracking>\n" +
                 "            </VideoClicks>\n" +
                 "            <TrackingEvents>\n" +
                 "              <Tracking event=\"start\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=start]]></Tracking>\n" +
                 "              <Tracking event=\"complete\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=complete]]></Tracking>\n" +
                 "              <Tracking event=\"firstQuartile\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=firstQuartile]]></Tracking>\n" +
                 "              <Tracking event=\"midpoint\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=midpoint]]></Tracking>\n" +
                 "              <Tracking event=\"thirdQuartile\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=thirdQuartile]]></Tracking>\n" +
                 "              <Tracking event=\"close\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=close]]></Tracking>\n" +
                 "            </TrackingEvents>\n" +
                 "          </Linear>\n" +
                 "        </Creative>\n" +
                 "        <Creative>\n" +
                 "          <CompanionAds>\n" +
                 "            <Companion height=\"1280\" width=\"720\">\n" +
                 "              <StaticResource creativeType=\"image/jpg\"><![CDATA[https://dsp-static.clickhubs.com/46930/0925w_my_720_wyl_fcy.jpg]]></StaticResource>\n" +
                 "              <TrackingEvents>\n" +
                 "                <Tracking event=\"creativeView\"><![CDATA[https://panda.clickhubs.com/event?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034&c=creativeView]]></Tracking>\n" +
                 "              </TrackingEvents>\n" +
                 "              <CompanionClickThrough><![CDATA[https://panda.clickhubs.com/fall/back?bundle=com.zzkko&clickid=xrktksop9wumnssnruqlmlvlpvsrtopsiljlnkkl9kliojmniqnk]]></CompanionClickThrough>\n" +
                 "              <CompanionClickTracking><![CDATA[https://app.appsflyer.com/com.zzkko?af_channel=&af_sub1=C_140420&af_sub2=5034&af_sub3=741&redirect=false&c=SHEIN-TaurusX-APPRMT-newlogic-MY-ANDROID-ALL-RTA-D&af_adset_id=1000568&af_reengagement_window=1d&af_click_lookback=1d&advertising_id=627ff7bd-c968-40e5-999c-c8b25862a9f4&idfa=&af_ip=**************&pid=taurus_int&af_cost_model=cpa&is_retargeting=true&af_ua=Mozilla%2F5.0+%28Linux%3B+Android+14%3B+SM-S908E+Build%2FUP1A.231005.007%3B+wv%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Version%2F4.0+Chrome%2F130.0.6723.108+Mobile+Safari%2F537.36&af_adset=shein_taurusx_android_my_rmt_RTA_D_Other_20240621_tagid_1960_mixcatid_351&af_c_id=100271&clickid=xrktksop9wumnssnruqlmlvlpvsrtopsiljlnkkl9kliojmniqnk&af_cost_value=7.2&af_siteid=zQdi-OCGalhuOuGKYHxb2U4RCMAix7YHYEaYuqoBgBbesarXFqtawny7Xd_IHgwX&af_ad_id=10007148&af_lang=en&af_ad=my_Other_video_9x16_20240621B_0001]]></CompanionClickTracking>\n" +
                 "              <CompanionClickTracking><![CDATA[https://panda.clickhubs.com/click?e=hSvNCHtJJjuDWP_hHdVGQpeRGarEZMW7ckD5J6UVBWF30_VsYjBdSpUEsuSXZroQpdhtX5gcQzTX35xCaSSjRz1iT3WgSjI2qR-4HvJJ07I0X-9EWZ05NvDcglt_sqzSKTw7px_rr_IuoCaxv4wfz2XikX_hyJ4GlM5EfCfhQEir3Dzx7SlGBcMMDa6FdJhg_CsCGIzW4s9E3nPcy73VVWiyTqvZsdFC_4UhRmzfzyA-qwIsiolKNZOAL7eR_ygRfWo1ALLLYwiyIv1s894ZhqJR0QJTjdGiWLusXzUmATeCWvdugXbct-i78bcvhz1Z20mWmsW5_Lych97kDum3-X9WRnF7eFG4y5yQej8mb5VxB5Xr623WC-ovFo8Q0BGHrS-ZuttJgPZPHz426xV9sDcno2hLo0nAt7kgYIiwlC9vC_gspCzZtUMux5wcXeGng8hwX-2jnzt24CMooNyVhxozDIEYKAoFCt52QHQ54ILwzr-YYZgub2MlYmOoeTGZG4TFoB5IJfBs37Edi5FSkTp2onRb9NWXhRG8Pbi17dOd94JqqPW6rX-rT5Nvyp4f6l8a2rh5w9OzYobrDlPkrw&n=5034]]></CompanionClickTracking>\n" +
                 "              <CompanionClickTracking><![CDATA[https://rtbtrack-asia.rtblab.net/clk?p=550&d=182&t=dmpNKcXFwX8T5oy34Ktpf5u-AYlO7uEksbPI-4OvNfAQ8C-Wv8ljXLFdr0kvQejPKRdCrgFEScpouANK2vTEkYS6xHkP0fXBc6yAhGJq7d4WduyqRdon_cKtaZFxJnlDD8YbF8-WDuVTIYji4_qRjMuflJt6I9tTsHBiZAFEstdRw4L-o0aCEensmxLrIz9u7K2uR7QigQ8XoakEl5N-wl-KsjzsAlFBHwl-3EgSaiPJEkehhOlfXW-I8iCM8guXuXkkncFc1dV6gyenU7NqFoAsIm4g9kTn014UnR4qT81oGK6MWoHeRYH7Sxesw3_9N0TTmaJb0JJuVNIJW_Xx2SUo72fZMn1qUCwLMHBlv_fBTnNmlVkfydVQgmFBYW1nlzYvHKfsoXTt_jA0j8Ed0IkYWSNfBDoXGOlxmdC9d77QoE5e4otIQUtfHTn_pnloRhYU6jbwmdOcaOKTrcJtr7C2ig7zVxPL0MK6-drwSM88Gq7oGz4vjYrfblWmZUEGiEts0T_cywCRjAPPqfn33JrYb0TnsAqpiMud7Q4_c9jugPUqs4M26Y3OlBTUZ5lK&tx=&ec=1]]></CompanionClickTracking>\n" +
                 "            </Companion>\n" +
                 "          </CompanionAds>\n" +
                 "        </Creative>\n" +
                 "      </Creatives>\n" +
                 "    </InLine>\n" +
                 "  </Ad>\n" +
                 "</VAST>";
         String viewLink = "https://test.growone.sg/test?info=testtsettesttset";
         int type = 2;
-        return injectProvider.injectView(adm,viewLink,type);
+        return injectProvider.injectView(adm, viewLink, type);
+    }
+
+    @GetMapping("/sspQps")
+    public Map<String, Double> sspQps() {
+        return dspEpQpsProvider.getSspQps();
+    }
+
+    @GetMapping("/dspQps")
+    public Map<String, Double> getDspQps() {
+        return dspEpQpsProvider.getDspEpReqQps();
+    }
+
+    @GetMapping("/dspBid")
+    public Map<String, Double> getDspBid() {
+        return dspEpQpsProvider.getDspEpBidQps();
+    }
+
+    @GetMapping("/ctrlQps")
+    public Map<String, Integer> getCtrlQps() {
+        return dspEpQpsProvider.getDspEpCtrlQps();
     }
 
     public static void main(String[] args) {
         System.out.println(testInject());
     }
 }
diff --git a/src/main/java/com/iflytek/traffic/data/entity/DspEpQps.java b/src/main/java/com/iflytek/traffic/data/entity/DspEpQps.java
index b0a91ce..ae104e5 100644
--- a/src/main/java/com/iflytek/traffic/data/entity/DspEpQps.java
+++ b/src/main/java/com/iflytek/traffic/data/entity/DspEpQps.java
@@ -1,42 +1,151 @@
 package com.iflytek.traffic.data.entity;
 
+import cn.hutool.core.collection.CollUtil;
+import cn.hutool.core.map.MapUtil;
+import com.google.common.collect.Lists;
 import lombok.Getter;
 import lombok.Setter;
 
-import java.util.HashMap;
-import java.util.Map;
+import java.util.*;
 
 @Getter
 @Setter
 public class DspEpQps {
 
     private Integer dspEpId;
 
     private Map<Integer, SingleQpsConfig> singleQpsConfigMap = new HashMap<>();
 
+    public List<KeyAndRatio<String, Double>> genAllConfigQpsKeyAndRatio() {
+        List<KeyAndRatio<String, Double>> ssp = getAllConfigKeyAndRatio(1001);
+        List<KeyAndRatio<String, Double>> bundle = getAllConfigKeyAndRatio(1003);
+        List<KeyAndRatio<String, Double>> region = getAllConfigKeyAndRatio(1004);
+        List<KeyAndRatio<String, Double>> adType = getAllConfigKeyAndRatio(1005);
+        return combineLists(ssp, adType, region, bundle);
+    }
+
+    private static List<KeyAndRatio<String, Double>> combineLists(List<KeyAndRatio<String, Double>>... lists) {
+        List<KeyAndRatio<String, Double>> result = new ArrayList<>();
+        if (lists == null) return result;
+
+        result.add(new KeyAndRatio<String, Double>("", 1.0d));  // 初始化空组合
+
+        for (List<KeyAndRatio<String, Double>> list : lists) {
+            if (list == null || list.isEmpty()) continue;  // 跳过空或null列表
+
+            List<KeyAndRatio<String, Double>> newCombinations = new ArrayList<>();
+            for (KeyAndRatio<String, Double> res : result) {
+                for (KeyAndRatio<String, Double> element : list) {
+                    String key = res.key.isBlank() ? element.key : res.key + "|" + element.key;  // 构建新键
+                    Double ratio = res.ratio * element.ratio / 100;  // 计算新比例
+                    newCombinations.add(new KeyAndRatio<String, Double>(key, ratio));  // 构建新组合
+                }
+            }
+            result = newCombinations;  // 更新结果集
+        }
+        return result;
+    }
+
+    public List<KeyAndRatio<String, Double>> genQpsConfigKeyAndRatio(Integer sspId, Integer adType, Long region, String bundle) {
+        KeyAndRatio<String, Double> sspKr = getConfigKeyAndRatio(1001, String.valueOf(sspId));
+        KeyAndRatio<String, Double> adTypeKr = getConfigKeyAndRatio(1005, String.valueOf(adType));
+        KeyAndRatio<String, Double> regionKr = getConfigKeyAndRatio(1004, String.valueOf(region));
+        KeyAndRatio<String, Double> bundleKr = getConfigKeyAndRatio(1003, bundle);
+        List<KeyAndRatio<String, Double>> keyAndRatios = Lists.newArrayList(sspKr, adTypeKr, regionKr, bundleKr);
+        List<KeyAndRatio<String, Double>> afterFilter = keyAndRatios.stream().filter(Objects::nonNull).toList();
+        if (CollUtil.isEmpty(afterFilter)) {
+            return null;
+        }
+        return afterFilter;
+    }
+
+    private KeyAndRatio<String, Double> getConfigKeyAndRatio(Integer configId, String value) {
+        if (!singleQpsConfigMap.containsKey(configId)) {
+            return null;
+        }
+        SingleQpsConfig singleQpsConfig = singleQpsConfigMap.get(configId);
+        if (!singleQpsConfig.getValue2Ratio().containsKey(value)) {
+            if (singleQpsConfig.getTotalRatio() < 100.0d) {
+                return new KeyAndRatio<String, Double>(configId + "_" + "others", 100.0d - singleQpsConfig.getTotalRatio());
+            } else {
+                return new KeyAndRatio<String, Double>(configId + "_" + "miss", 0.0d);
+            }
+        }
+        return new KeyAndRatio<>(configId + "_" + value, singleQpsConfig.getValue2Ratio().get(value));
+    }
+
+    private List<KeyAndRatio<String, Double>> getAllConfigKeyAndRatio(Integer configId) {
+        if (!singleQpsConfigMap.containsKey(configId)) {
+            return null;
+        }
+        SingleQpsConfig singleQpsConfig = singleQpsConfigMap.get(configId);
+        if (MapUtil.isEmpty(singleQpsConfig.getValue2Ratio())) {
+            return null;
+        }
+        List<KeyAndRatio<String, Double>> keyAndRatios = Lists.newArrayList();
+        for (Map.Entry<String, Double> entry : singleQpsConfig.getValue2Ratio().entrySet()) {
+            keyAndRatios.add(new KeyAndRatio<>(configId + "_" + entry.getKey(), entry.getValue()));
+        }
+        if (singleQpsConfig.getTotalRatio() < 100.0d) {
+            keyAndRatios.add(new KeyAndRatio<>(configId + "_" + "others", 100.0d - singleQpsConfig.getTotalRatio()));
+        }
+        return keyAndRatios;
+    }
+
+    public record KeyAndQps<String, Integer>(String key, Integer qps) {
+    }
+
+    public record KeyAndRatio<String, Double>(String key, Double ratio) {
+    }
+
     @Getter
     @Setter
     public static class SingleQpsConfig {
 
+        // configId：1001 ssp，configId：1003 包名，configId：1004 地域，configId：1005 广告形式
         private Integer configId;
 
-        private Integer total = 0;
+        private Double totalRatio = 0.0;
 
-        private Map<String, Integer> value2Qps = new HashMap<>();
+        private Map<String, Double> value2Ratio = new HashMap<>();
 
     }
 
     @Getter
     @Setter
     public static class DspEpQpsConfig {
 
         private Integer dspEpId;
 
         private Integer configId;
 
+        private Double ratio;
+
         private String configVal;
 
         private Integer qps;
     }
 
+    public static void main(String[] args) {
+        List<KeyAndRatio<String, Double>> ssp = Lists.newArrayList(
+                new KeyAndRatio<>("ssp1", 50.0d),
+                new KeyAndRatio<>("ssp2", 50.0d)
+        );
+        List<KeyAndRatio<String, Double>> adType = Lists.newArrayList(
+                new KeyAndRatio<>("100", 60.0d),
+                new KeyAndRatio<>("400", 30.0d),
+                new KeyAndRatio<>("others", 10.0d)
+        );
+        List<KeyAndRatio<String, Double>> region = Lists.newArrayList(
+//                new KeyAndRatio<>("1000000000", 70.0d),
+//                new KeyAndRatio<>("200000000", 30.0d)
+        );
+        List<KeyAndRatio<String, Double>> bundle = Lists.newArrayList(
+                new KeyAndRatio<>("com.iflytek.test", 50.0d),
+                new KeyAndRatio<>("others", 50.0d)
+        );
+        List<KeyAndRatio<String, Double>> combined = combineLists(ssp, adType, region, bundle);
+        System.out.println(combined);
+    }
+
 }
\ No newline at end of file
diff --git a/src/main/java/com/iflytek/traffic/data/mapper/info/DspEpQpsMapper.java b/src/main/java/com/iflytek/traffic/data/mapper/info/DspEpQpsMapper.java
index f7c8486..a885fa8 100644
--- a/src/main/java/com/iflytek/traffic/data/mapper/info/DspEpQpsMapper.java
+++ b/src/main/java/com/iflytek/traffic/data/mapper/info/DspEpQpsMapper.java
@@ -7,7 +7,7 @@ import java.util.List;
 
 public interface DspEpQpsMapper {
 
-    @Select("select dsp_ep_id, config_id, config_val, qps from iflytek_overseas_adx.d_dsp_ep_qps where is_del = 0")
+    @Select("select dsp_ep_id, config_id, ratio, config_val, qps from iflytek_overseas_adx.d_dsp_ep_qps where is_del = 0")
     List<DspEpQps.DspEpQpsConfig> selectValidConfig();
 
 }
diff --git a/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java b/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java
index f83acf0..6841f8f 100644
--- a/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java
+++ b/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java
@@ -18,91 +18,99 @@ import java.util.stream.Collectors;
 @Service
 @Slf4j
 public class DspDataProvider {
 
 
     @Autowired
     private DspMapper dspMapper;
 
     public Map<Integer, DspEpInfo> dspEpInfoMap = new HashMap<>();
 
 
     @Scheduled(fixedRateString = "${data.update.interval.dsp:300000}")
     public void update(){
         updateDspEpInfo();
     }
 
     /**
      * 定时刷新ep信息
      */
     public void updateDspEpInfo(){
         List<DspEpInfo> dspEpInfoList = dspMapper.selectValidEp();
         if (CollectionUtil.isEmpty(dspEpInfoList)) {
             log.error("update with empty dsp ep infos");
             return;
         }
         log.info("update dsp ep info, size: {}", dspEpInfoList.size());
         Iterator<DspEpInfo> iterator = dspEpInfoList.iterator();
         while (iterator.hasNext()) {
             DspEpInfo dspEpInfo = iterator.next();
             if (StrUtil.isBlank(dspEpInfo.getPrefix()) || dspEpInfo.getPrefix().equals("unknown")) {
                 log.warn("dsp ep path is blank or path is unknown: {}", dspEpInfo.getId());
                 iterator.remove();
             }
             if (StrUtil.isBlank(dspEpInfo.getProtocol())) {
                 log.warn("dsp ep protocol is blank: {}", dspEpInfo.getId());
                 iterator.remove();
             }
             if (StrUtil.isBlank(dspEpInfo.getPath())) {
                 log.warn("dsp ep path is blank: {}", dspEpInfo.getId());
                 iterator.remove();
             }
         }
         Map<Integer, DspEpInfo> tmp = dspEpInfoList.stream().collect(Collectors.toMap(DspEpInfo::getId, Function.identity()));
         dspEpInfoMap = tmp;
     }
 
     public DspEpObj getDspEpObj(Integer dspEpId) {
         if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
             DspEpObj dspEpObj = new DspEpObj();
             DspEpInfo info = dspEpInfoMap.get(dspEpId);
             dspEpObj.setDspId(info.getDspId());
             dspEpObj.setDspEpId(info.getId());
             dspEpObj.setQps(Long.valueOf(info.getQps()).intValue());
             dspEpObj.setPath(info.getPath());
             dspEpObj.setPrefix(info.getPrefix());
             dspEpObj.setName(info.getDspName());
             dspEpObj.setTimeout(info.getTimeout());
             dspEpObj.setIsGzip(info.getIsGzip());
             dspEpObj.setSettlementType(info.getSettlementType());
             dspEpObj.setProtocol(info.getProtocol());
             dspEpObj.setImpTtl(info.getDspImpTimeout());
             if (info.getImpTimeout() != null && info.getImpTimeout() > 0) {
                 dspEpObj.setImpTtl(info.getImpTimeout());
             }
             return dspEpObj;
         }
         return null;
     }
 
     public List<Integer> diffDspEpId(Set<Integer> dspEpId) {
         if (CollUtil.isEmpty(dspEpId)) {
             return CollUtil.newArrayList(dspEpInfoMap.keySet());
         }
         List<Integer> res = CollUtil.newArrayList();
         for (Integer id : dspEpInfoMap.keySet()) {
             if (!dspEpId.contains(id)) {
                 res.add(id);
             }
         }
         return res;
     }
 
     public Integer getDspId(Integer dspEpId) {
         if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
             DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
             return dspEpInfo.getDspId();
         }
         return null;
     }
 
+    public Integer getDspEpQps(Integer dspEpId) {
+        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
+            DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
+            return Long.valueOf(dspEpInfo.getQps()).intValue();
+        }
+        return null;
+    }
+
 }
diff --git a/src/main/java/com/iflytek/traffic/data/provider/DspEpQpsProvider.java b/src/main/java/com/iflytek/traffic/data/provider/DspEpQpsProvider.java
index 87401cf..d3f3526 100644
--- a/src/main/java/com/iflytek/traffic/data/provider/DspEpQpsProvider.java
+++ b/src/main/java/com/iflytek/traffic/data/provider/DspEpQpsProvider.java
@@ -1,21 +1,23 @@
 package com.iflytek.traffic.data.provider;
 
 import cn.hutool.core.collection.CollUtil;
+import cn.hutool.core.map.MapUtil;
+import cn.hutool.core.util.StrUtil;
 import com.iflytek.traffic.data.entity.DspEpQps;
 import com.iflytek.traffic.data.mapper.info.DspEpQpsMapper;
 import com.iflytek.traffic.dsp.DspEpObj;
-import com.iflytek.traffic.session.request.Device;
 import com.iflytek.traffic.session.request.Impression;
 import com.iflytek.traffic.session.request.UnifiedRequest;
+import com.iflytek.traffic.ssp.SspEp;
+import com.iflytek.traffic.util.SpringContextHelper;
+import com.iflytek.traffic.util.qps.DimensionalQpsMonitorCache;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.scheduling.annotation.Scheduled;
 import org.springframework.stereotype.Service;
 
-import java.util.ArrayList;
-import java.util.HashMap;
-import java.util.List;
-import java.util.Map;
+import java.util.*;
+import java.util.concurrent.ConcurrentHashMap;
 import java.util.stream.Collectors;
 
 @Service
@@ -21,90 +23,252 @@ import java.util.stream.Collectors;
 @Service
 @Slf4j
 public class DspEpQpsProvider {
 
     @Autowired
     private DspEpQpsMapper dspEpQpsMapper;
 
+    private DimensionalQpsMonitorCache qpsCache = new DimensionalQpsMonitorCache();
+
+    private DimensionalQpsMonitorCache dspEpReqCache = new DimensionalQpsMonitorCache();
+
+    private DimensionalQpsMonitorCache dspEpBidCache = new DimensionalQpsMonitorCache();
+
     private volatile Map<Integer, DspEpQps> dspEpQpsMap = new HashMap<>();
 
+    private Map<String, DspEpQps.KeyAndQps<String, Integer>> dspEpKeyAndQpsMap = new ConcurrentHashMap<>();
+
     @Scheduled(fixedRateString = "${data.update.interval.dspEp-qps:300000}")
     public void update() {
         updateDspEpQps();
     }
 
     private void updateDspEpQps() {
 
         List<DspEpQps.DspEpQpsConfig> dspEpQpsConfigs = dspEpQpsMapper.selectValidConfig();
         if (CollUtil.isEmpty(dspEpQpsConfigs)) {
             log.error("update with empty dsp ep qps config");
             return;
         }
         log.info("update dsp ep qps config, size: {}", dspEpQpsConfigs.size());
         Map<Integer, List<DspEpQps.DspEpQpsConfig>> groupedConfigs = dspEpQpsConfigs.stream()
                 .collect(Collectors.groupingBy(DspEpQps.DspEpQpsConfig::getDspEpId));
         Map<Integer, DspEpQps> temp = new HashMap<>();
         groupedConfigs.forEach((k, v) -> {
             DspEpQps dspEpQps = temp.computeIfAbsent(k, id -> new DspEpQps());
             dspEpQps.setDspEpId(k);
             v.forEach(config -> {
                 DspEpQps.SingleQpsConfig singleQpsConfig = dspEpQps.getSingleQpsConfigMap()
                         .computeIfAbsent(config.getConfigId(), id -> new DspEpQps.SingleQpsConfig());
                 singleQpsConfig.setConfigId(config.getConfigId());
-                singleQpsConfig.getValue2Qps().put(config.getConfigVal(), config.getQps());
-                singleQpsConfig.setTotal(singleQpsConfig.getTotal() + config.getQps());
+                singleQpsConfig.getValue2Ratio().put(config.getConfigVal(), config.getRatio());
+                singleQpsConfig.setTotalRatio(singleQpsConfig.getTotalRatio() + config.getRatio());
             });
         });
         dspEpQpsMap = temp;
     }
 
-    public record KeyAndQps<String, Integer>(String key, Integer qps) {}
+    public DspEpQps.KeyAndQps<String, Integer> getDspEpQpsByReq(SspEp sspEp, DspEpObj dspEp, UnifiedRequest request) {
+        // 按配置的维度叉乘后，进行最细粒度的QPS控制
+        List<DspEpQps.KeyAndRatio<String, Double>> keyAndRatios = getQpsKeyAndRatio(sspEp, dspEp.getDspEpId(), request);
+        if (CollUtil.isEmpty(keyAndRatios)) {
+            return null;
+        }
+        // 读取最新QPS配置
+        StringBuilder key = new StringBuilder("DSPEP_" + dspEp.getDspEpId() + "_QPS");
+        Integer qps = dspEp.getQps();
+        for (DspEpQps.KeyAndRatio<String, Double> kr : keyAndRatios) {
+            key.append("_").append(kr.key());
+            qps = (int) Math.ceil(qps * kr.ratio().intValue() / 100.0d);
+        }
+        if (dspEpKeyAndQpsMap.containsKey(key.toString())) {
+            return dspEpKeyAndQpsMap.get(key.toString());
+        }
+        return new DspEpQps.KeyAndQps<>(key.toString(), qps);
+    }
 
-    public List<KeyAndQps<String, Integer>> getDspEpQpsByReq(DspEpObj dspEpObj, UnifiedRequest request) {
-        DspEpQps dspEpQps = dspEpQpsMap.get(dspEpObj.getDspEpId());
+    private List<DspEpQps.KeyAndRatio<String, Double>> getQpsKeyAndRatio(SspEp sspEp, Integer dspEpId, UnifiedRequest request) {
+        DspEpQps dspEpQps = dspEpQpsMap.get(dspEpId);
         if (dspEpQps == null || CollUtil.isEmpty(dspEpQps.getSingleQpsConfigMap())) {
-            log.info("dsp ep {} qps not found.", dspEpObj.getDspEpId());
-            return List.of();
+            log.info("dsp ep {} qps config not found.", dspEpId);
+            return null;
         }
-        List<KeyAndQps<String, Integer>> result = new ArrayList<>();
-        // 广告形式, configId: 1005
-        DspEpQps.SingleQpsConfig adTypeConfig = dspEpQps.getSingleQpsConfigMap().get(1005);
-        if (adTypeConfig != null) {
-            Impression imp = request.getImps().values().stream().findAny().orElse(null);
-            Integer adType = null;
-            if (imp != null) {
-                adType = imp.getAdType().getValue();
+        Integer sspId = sspEp.getSspId();
+        String bundle = request.getApp() != null && StrUtil.isNotBlank(request.getApp().getBundle()) ? request.getApp().getBundle() : null;
+        Long region = request.getDevice() != null && request.getDevice().getRegionInfo() != null ? request.getDevice().getRegionInfo().getRegion() : null;
+        Impression imp = request.getImps().values().stream().filter(Objects::nonNull).findAny().orElse(null);
+        Integer adType = imp != null ? imp.getAdType().getValue() : null;
+        return dspEpQps.genQpsConfigKeyAndRatio(sspId, adType, region, bundle);
+    }
+
+    @Scheduled(initialDelayString = "${data.refresh.interval.dspEp-qps:30000}", fixedRateString = "${data.refresh.interval.dspEp-qps:30000}")
+    public void refreshDspEpQps() {
+        // 计算DSP EP的QPS
+        calDspEpQps();
+    }
+    /**
+     * 计算DSP EP的QPS
+     */
+    private void calDspEpQps() {
+        log.info("start cal dsp ep qps.");
+        if (MapUtil.isEmpty(dspEpQpsMap)) {
+            return;
+        }
+        // 按DSP EP 逐个刷新新的QPS
+        DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
+        for (Map.Entry<Integer, DspEpQps> entry : dspEpQpsMap.entrySet()) {
+            Integer dspEpId = entry.getKey();
+            DspEpQps dspEpQps = entry.getValue();
+            if (CollUtil.isEmpty(dspEpQps.getSingleQpsConfigMap())) {
+                continue;
+            }
+            Integer totalQps = dspDataProvider.getDspEpQps(dspEpId);
+            if (totalQps == null || totalQps <= 0) {
+                log.warn("dsp ep {} no total qps limit.", dspEpId);
+                continue;
+            }
+            // 计算每个DSP EP配置的QPS
+            List<DspEpQps.KeyAndRatio<String, Double>> keyAndRatios = dspEpQps.genAllConfigQpsKeyAndRatio();
+            if (CollUtil.isEmpty(keyAndRatios)) {
+                continue;
             }
-            if (adType != null) {
-                Map<String, Integer> adType2QpsConfig = adTypeConfig.getValue2Qps();
-                KeyAndQps<String, Integer> kq = adType2QpsConfig.containsKey(String.valueOf(adType)) ?
-                        new KeyAndQps<String, Integer>("DSP_EP_" + dspEpQps.getDspEpId() + "_ADTYPE_" + adType + "_QPS_CTRL", adType2QpsConfig.get(String.valueOf(adType)))
-                        : new KeyAndQps<String, Integer>("DSP_EP_" + dspEpQps.getDspEpId() + "_ADTYPE_others_QPS_CTRL", dspEpObj.getQps() - adTypeConfig.getTotal());
-                if (kq.qps() != null && kq.qps() > 0) {
-                    result.add(kq);
+            List<QPS<Double, String>> qpsData = new ArrayList<>();
+            double gap = 0.0;
+            for (DspEpQps.KeyAndRatio<String, Double> kr : keyAndRatios) {
+                Double sspQps = qpsCache.getQps(kr.key());
+                Double reqQps = dspEpReqCache.getQps(kr.key());
+                Double bid = dspEpBidCache.getQps(kr.key());
+                QPS<Double, String> qps = null;
+                if (reqQps != 0.0) {
+                    qps = new QPS<Double, String>(kr.key(), sspQps, reqQps, bid / reqQps);
+                } else {
+                    qps = new QPS<Double, String>(kr.key(), sspQps, reqQps, 0.0);
+                }
+                log.info("cal dsp ep {} qps, key: {}, qps ratio {}, sspQps {}, reqQps {}, bid {}, bidRatio: {}.",
+                        dspEpId, kr.key(), kr.ratio(), sspQps, reqQps, bid, qps.bidRatio);
+                qpsData.add(qps);
+                if (reqQps < totalQps * kr.ratio()) {
+                    double krQpsGap = totalQps * kr.ratio() - reqQps;
+                    log.info("cal dsp ep {} qps, key: {} not enough, qps ratio {}, sspQps: {}, reqQps: {}, bid ratio: {}, qps gap: {}",
+                            dspEpId, kr.key(), kr.ratio(), sspQps, reqQps, qps.bidRatio, krQpsGap);
+                    gap += krQpsGap;
+                } else {
+                    log.info("cal dsp ep {} qps, key: {} enough, qps ratio {}, sspQps: {}, reqQps: {}, bid ratio: {}",
+                            dspEpId, kr.key(), kr.ratio(), sspQps, reqQps, qps.bidRatio);
+                    // 先删除缓存
+                    String[] kp = qps.key().split("\\|");
+                    StringBuilder qpsCtrlKey = new StringBuilder("DSPEP_" + dspEpId + "_QPS");
+                    for (String param : kp) {
+                        qpsCtrlKey.append("_").append(param);
+                    }
+                    dspEpKeyAndQpsMap.remove(qpsCtrlKey.toString());
                 }
             }
-        }
-        // 地域, configId: 1004
-        DspEpQps.SingleQpsConfig regionConfig = dspEpQps.getSingleQpsConfigMap().get(1004);
-        if (regionConfig != null) {
-            Device.RegionInfo regionInfo = request.getDevice() != null ? request.getDevice().getRegionInfo() : null;
-            KeyAndQps<String, Integer> kq = null;
-            if (regionInfo == null || regionInfo.getRegion() == null) {
-                // 如果没有地域信息，则使用默认配置
-                kq = new KeyAndQps<String, Integer>("DSP_EP_" + dspEpQps.getDspEpId() + "_REGION_others_QPS_CTRL", dspEpObj.getQps() - regionConfig.getTotal());
-
-            } else {
-                String region = String.valueOf(regionInfo.getRegion());
-                Map<String, Integer> region2QpsConfig = regionConfig.getValue2Qps();
-                kq = region2QpsConfig.containsKey(region) ?
-                        new KeyAndQps<String, Integer>("DSP_EP_" + dspEpQps.getDspEpId() + "_REGION_" + region + "_QPS_CTRL", region2QpsConfig.get(region))
-                        : new KeyAndQps<String, Integer>("DSP_EP_" + dspEpQps.getDspEpId() + "_REGION_others_QPS_CTRL", dspEpObj.getQps() - regionConfig.getTotal());
+            // 按bidRatio排序
+
+            Collections.sort(qpsData, new Comparator<QPS<Double, String>>() {
+                @Override
+                public int compare(QPS<Double, String> o1, QPS<Double, String> o2) {
+                    return Double.compare(o2.bidRatio(), o1.bidRatio()); // 降序排序
+                }
+            });
+            if (gap > 0.0) {
+                // 如果有gap，说明当前QPS没有达到配置的QPS，需要进行调整
+                // 调整规则：按参竞率从高到低分配gap值，分配额= sspQps - reqQps
+                log.info("dsp ep {} total qps gap: {}, start adjust.", dspEpId, gap);
+                for (QPS<Double, String> qps : qpsData) {
+                    if (qps.sspQps() - qps.reqQps() > 0.0) {
+                        double adjust = Math.min(gap, qps.sspQps() - qps.reqQps());
+                        if (adjust > 0.0) {
+                            int newQps = (int) Math.ceil(qps.reqQps() + adjust);
+                            log.info("cal dsp ep {} qps, adjust key: {}, sspQps: {}, reqQps: {}, bid ratio: {}, adjust: {}",
+                                    dspEpId, qps.key(), qps.sspQps(), qps.reqQps(), qps.bidRatio(), adjust);
+                            // 更新缓存
+                            String[] kp = qps.key().split("\\|");
+                            StringBuilder qpsCtrlKey = new StringBuilder("DSPEP_" + dspEpId + "_QPS");
+                            for (String param : kp) {
+                                qpsCtrlKey.append("_").append(param);
+                            }
+                            dspEpKeyAndQpsMap.put(qpsCtrlKey.toString(), new DspEpQps.KeyAndQps<>(qpsCtrlKey.toString(), newQps));
+                            gap -= adjust;
+                        }
+                    }
+                    if (gap <= 0.0) {
+                        break; // gap已分配完毕
+                    }
+                }
             }
-            if (kq.qps() != null && kq.qps() > 0) {
-                result.add(kq);
+
+        }
+    }
+
+    /**
+     * QPS记录
+     * @param key 控制单元key
+     * @param sspQps ssp侧符合要求的QPS
+     * @param reqQps 实际请求DSP的QPS
+     * @param bidRatio 参竞率
+     */
+    record QPS<Double, String>(String key, Double sspQps, Double reqQps, Double bidRatio) {}
+
+    private Set<String> genRecordKey(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
+        if (CollUtil.isEmpty(dspEpIds)) {
+            return null;
+        }
+        Set<String> recordKey = new HashSet<>();
+        for (Integer dspEpId : dspEpIds) {
+            List<DspEpQps.KeyAndRatio<String, Double>> keyAndRatios = getQpsKeyAndRatio(sspEp, dspEpId, request);
+            if (CollUtil.isEmpty(keyAndRatios)) {
+                continue;
             }
+            String key = keyAndRatios.stream().map(DspEpQps.KeyAndRatio::key).collect(Collectors.joining("|"));
+            recordKey.add(key);
+        }
+        if (CollUtil.isEmpty(recordKey)) {
+            return null;
+        }
+        return recordKey;
+    }
+
+    public void recordAfterIndexDspEpQps(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
+        Set<String> recordKey = genRecordKey(sspEp, request, dspEpIds);
+        if (recordKey == null) return;
+        for (String key : recordKey) {
+            qpsCache.recordRequest(key);
+        }
+    }
+
+    public void recordDspEpReqQps(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
+        Set<String> recordKey = genRecordKey(sspEp, request, dspEpIds);
+        if (recordKey == null) return;
+        for (String key : recordKey) {
+            dspEpReqCache.recordRequest(key);
         }
+    }
+
+    public void recordDspEpBidQps(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
+        Set<String> recordKey = genRecordKey(sspEp, request, dspEpIds);
+        if (recordKey == null) return;
+        for (String key : recordKey) {
+            dspEpBidCache.recordRequest(key);
+        }
+    }
+
+    public Map<String, Double> getSspQps() {
+        return qpsCache.getAllQps();
+    }
+
+    public Map<String, Double> getDspEpReqQps() {
+        return dspEpReqCache.getAllQps();
+    }
+
+    public Map<String, Double> getDspEpBidQps() {
+        return dspEpBidCache.getAllQps();
+    }
+
+    public Map<String, Integer> getDspEpCtrlQps() {
+        Map<String, Integer> result = new HashMap<>();
+        dspEpKeyAndQpsMap.forEach((k, v) -> result.put(k, v.qps()));
         return result;
     }
 
 }
diff --git a/src/main/java/com/iflytek/traffic/procedure/PickOneVerticle.java b/src/main/java/com/iflytek/traffic/procedure/PickOneVerticle.java
index 6e3067b..4a59905 100644
--- a/src/main/java/com/iflytek/traffic/procedure/PickOneVerticle.java
+++ b/src/main/java/com/iflytek/traffic/procedure/PickOneVerticle.java
@@ -4,6 +4,7 @@ import cn.hutool.core.collection.CollUtil;
 import cn.hutool.core.map.MapUtil;
 import cn.hutool.core.util.CharsetUtil;
 import cn.hutool.core.util.StrUtil;
+import com.iflytek.traffic.data.provider.DspEpQpsProvider;
 import com.iflytek.traffic.data.provider.InjectProvider;
 import com.iflytek.traffic.dsp.DspEpObj;
 import com.iflytek.traffic.log.LogService;
@@ -20,180 +21,187 @@ import lombok.extern.slf4j.Slf4j;
 import java.math.BigDecimal;
 import java.net.URLEncoder;
 import java.text.MessageFormat;
+import java.util.HashSet;
 import java.util.Map;
+import java.util.Set;
 
 @Slf4j
 @EventListener(listen = EventBusAddress.PICK_ONE)
 public class PickOneVerticle extends BasicVerticle {
     @Override
     void doHandle(SessionContext sessionContext) {
         if (MapUtil.isEmpty(sessionContext.getDspUnifiedResp())) {
             log.info("empty dsp response.");
             vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
             return;
         }
         // 选择出价最高的DSP
         Integer maxPriceDsp = null;
         Long maxPrice = -10000L;
+        Set<Integer> bidSuccessDspEp = new HashSet<>();
         for(Map.Entry<Integer, UnifiedResponse> entry : sessionContext.getDspUnifiedResp().entrySet()) {
             Integer k = entry.getKey();
             UnifiedResponse v = entry.getValue();
             if (v.getCode() != 200 || CollUtil.isEmpty(v.getSeatbids())
                     || CollUtil.isEmpty(v.getImpId2Bid())) {
                 log.info("dsp <{}> invalid response.", k);
                 continue;
             }
+            bidSuccessDspEp.add(k);
             // 如果有多个bid ，算总出价
             Long price = v.getSeatbids().get(0).getBids().get(0).getPrice();
             Map<String, Bid> impIdToBidMap = v.getImpId2Bid();
             if (impIdToBidMap.size() > 1) {
                 price = 0l;
                 UnifiedRequest res = sessionContext.getUnifiedRequest();
                 for (String imp : impIdToBidMap.keySet()) {
                     Bid bid = impIdToBidMap.get(imp);
                     Impression impression = res.getImps().get(imp);
                     price += bid.getPrice() - impression.getBidfloor();
                 }
             }
             if (price > maxPrice) {
                 maxPrice = price;
                 maxPriceDsp = k;
             }
         }
+        // 记录竞价成功QPS
+        DspEpQpsProvider dspEpQpsProvider = SpringContextHelper.getBean(DspEpQpsProvider.class);
+        dspEpQpsProvider.recordDspEpBidQps(sessionContext.getSspEp(), sessionContext.getUnifiedRequest(), bidSuccessDspEp);
         if (maxPriceDsp != null) {
             sessionContext.setWinner(maxPriceDsp);
             UnifiedResponse winRsp = sessionContext.getDspUnifiedResp().get(maxPriceDsp);
             for (Bid bid : winRsp.getImpId2Bid().values()) {
                 // 记录获胜价格&原始价格
                 bid.setOriginPrice(bid.getPrice());
                 bid.setWinPrice(maxPrice);
                 // 替换价格宏
                 DspEpObj winDspEp = sessionContext.getResponseDspEpObj().get(sessionContext.getWinner());
                 sessionContext.setWinUResp(winRsp);
                 if (winDspEp != null) {
                     replaceAuctionPrice(winDspEp, bid, bid.getPrice());
                     // 修改对下游的出价
                     modifyBidPrice(sessionContext, winDspEp, bid, bid.getPrice());
                     // 添加我方监测链接
                     addMonitorUrl(sessionContext, winDspEp, bid);
                 }
             }
         }
         // 设置其他dsp过滤原因：出价
         setFilterReasonPrice(sessionContext);
         vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
     }
 
     void replaceAuctionPrice(DspEpObj dspEpObj, Bid bid, Long price) {
         BigDecimal pb = new BigDecimal(price);
         String pstr = pb.divide(new BigDecimal(Constants.PRICE_MULTIPLY_MILLIONS)).toString();
         // nurl 默认替换
         if (StrUtil.isNotBlank(bid.getNurl())) {
             bid.setNurl(bid.getNurl().replace("${AUCTION_PRICE}", pstr));
         }
         // adm
         if (dspEpObj.getSettlementType() == 1 && StrUtil.isNotBlank(bid.getAdm())) {
             bid.setAdm(bid.getAdm().replace("${AUCTION_PRICE}", pstr));
         }
         // burl
         if (dspEpObj.getSettlementType() == 2 && StrUtil.isNotBlank(bid.getBurl())) {
             bid.setBurl(bid.getBurl().replace("${AUCTION_PRICE}", pstr));
         }
     }
 
     void modifyBidPrice(SessionContext sessionContext, DspEpObj dspEpObj, Bid bid, Long winPrice) {
         // 记录获胜价格&原始价格
         bid.setOriginPrice(bid.getPrice());
         bid.setWinPrice(winPrice);
         // 修改对下游的出价
         Impression impression = sessionContext.getUnifiedRequest().getImps().get(bid.getImpId());
         Float profit = impression.getDspProfitRatio().get(dspEpObj.getDspEpId());
         // (winPrice - x) / winPrice = profit; x = winPrice * (1 - profit)
         if (profit != null && profit > 0) {
             // 计算出价
             Float price = winPrice * (1 - profit);
             // 四舍五入
             price = new BigDecimal(price).setScale(0, BigDecimal.ROUND_HALF_UP).floatValue();
             bid.setPrice(price.longValue());
         }
     }
 
     void addMonitorUrl(SessionContext sessionContext, DspEpObj dspEpObj, Bid bid) {
         String reqId = sessionContext.getUnifiedRequest().getMediaReqId();
         String bidId = sessionContext.getSessionId();
         String impId = bid.getImpId();
         Impression impression = sessionContext.getUnifiedRequest().getImps().get(impId);
         String paramInfo = LogService.genParamInfo(sessionContext, dspEpObj, impression);
         String win = impression.getSecure() != null && impression.getSecure() == 1 ? SpringContextHelper.getProperty("monitor.win.url.https")
                 : SpringContextHelper.getProperty("monitor.win.url.http");
         String winUrl = MessageFormat.format(win, paramInfo, reqId, bidId, impId);
         // 302 dsp win notice
         if (StrUtil.isNotBlank(bid.getNurl())) {
             winUrl = winUrl + "&landing=" + URLEncoder.encode(bid.getNurl(), CharsetUtil.CHARSET_UTF_8);
         }
         bid.setNurl(winUrl);
         // 处理曝光监测
         int type = switch (impression.getRequestAdType()) {
             case SUPPER_BANNER -> 1;
             case SUPPER_VIDEO -> 2;
             case SUPPER_NATIVE -> 3;
             case UNKNOWN -> 1;
             default -> 1;
         };
         // 结算类型，默认填0
         String st = "0";
         String monitorView = impression.getSecure() != null && impression.getSecure() == 1 ? SpringContextHelper.getProperty("monitor.view.url.https")
                 : SpringContextHelper.getProperty("monitor.view.url.http");
         String impressionUrl = MessageFormat.format(monitorView, paramInfo, reqId, bidId, impId, st, "${AUCTION_PRICE}");
         InjectProvider injectProvider = SpringContextHelper.getBean(InjectProvider.class);
         SspEp sspEp = sessionContext.getSspEp();
         // adm
         if (sspEp.getSettlementType() == 1) {
             // dsp 采用adm结算 直接注入
             if (dspEpObj.getSettlementType() == 1) {
                 String adm = injectProvider.injectView(bid.getAdm(), impressionUrl, type);
                 bid.setAdm(adm);
             }
             // dsp 采用burl结算, 302 dsp的burl然后注入
             if (dspEpObj.getSettlementType() == 2) {
                 String imp302 = impressionUrl + "&landing=" + URLEncoder.encode(bid.getBurl(), CharsetUtil.CHARSET_UTF_8);;
                 String adm = injectProvider.injectView(bid.getAdm(), imp302, type);
                 bid.setAdm(adm);
             }
         }
         // burl
         if (sspEp.getSettlementType() == 2) {
             // dsp 采用adm结算 burl字段填写我方监测即可
             if (dspEpObj.getSettlementType() == 1) {
                 bid.setBurl(impressionUrl);
             }
             // dsp 采用burl结算 302 dsp的burl即可
             if (dspEpObj.getSettlementType() == 2) {
                 String imp302 = impressionUrl + "&landing=" + URLEncoder.encode(bid.getBurl(), CharsetUtil.CHARSET_UTF_8);
                 bid.setBurl(imp302);
             }
         }
     }
 
     void setFilterReasonPrice(SessionContext sessionContext) {
         Integer winner = sessionContext.getWinner();
         for (int dspEpId : sessionContext.getDspUnifiedResp().keySet()) {
             if (winner != null && dspEpId == winner) {
                 continue;
             }
             UnifiedResponse response = sessionContext.getDspUnifiedResp().get(dspEpId);
             Map<String, Bid> impIdToBidMap = response.getImpId2Bid();
             if (CollUtil.isNotEmpty(impIdToBidMap)) {
                 for (Bid bid : impIdToBidMap.values()) {
                     // 设置过滤原因
                     if (bid.getFilterReason() == null) {
                         bid.setFilterReason(Constants.FILTER_REASON_PRICE);
                     }
 //                    response.getImp2FilterReason().putIfAbsent(bid.getImpId(), Constants.FILTER_REASON_PRICE);
                 }
             }
 
         }
     }
 
 }
diff --git a/src/main/java/com/iflytek/traffic/procedure/RequestDspVerticle.java b/src/main/java/com/iflytek/traffic/procedure/RequestDspVerticle.java
index 25fbc5b..b4a46ec 100644
--- a/src/main/java/com/iflytek/traffic/procedure/RequestDspVerticle.java
+++ b/src/main/java/com/iflytek/traffic/procedure/RequestDspVerticle.java
@@ -3,6 +3,7 @@ package com.iflytek.traffic.procedure;
 import cn.hutool.core.collection.CollUtil;
 import cn.hutool.core.map.MapUtil;
 import cn.hutool.core.util.StrUtil;
+import com.iflytek.traffic.data.entity.DspEpQps;
 import com.iflytek.traffic.data.provider.DspDataProvider;
 import com.iflytek.traffic.data.provider.DspEpQpsProvider;
 import com.iflytek.traffic.dsp.DspEpObj;
@@ -18,8 +19,6 @@ import io.vertx.core.http.HttpClient;
 import io.vertx.core.http.HttpClientOptions;
 import lombok.extern.slf4j.Slf4j;
 
-import java.util.HashSet;
-import java.util.List;
 import java.util.HashMap;
 import java.util.Map;
 import java.util.Set;
@@ -29,145 +28,152 @@ import java.util.concurrent.TimeUnit;
 @EventListener(listen = EventBusAddress.REQUEST_DSP)
 @Slf4j
 public class RequestDspVerticle extends BasicVerticle {
 
     private static final Map<Integer, HttpClient> dspClient = new ConcurrentHashMap<>();
 
     private static final Map<String, Counter> dspQpsFilterCounter = new ConcurrentHashMap<>();
 
     @Override
     void doHandle(SessionContext sessionContext) {
         // 确认需要放量的dsp ep id, 命中圈量策略的dsp ep + 未配置圈量策略的dsp ep
         Set<Integer> dspEpId = CollUtil.newHashSet();
         if (CollUtil.isNotEmpty(sessionContext.getNonConfigDspEpId())) {
             dspEpId.addAll(sessionContext.getNonConfigDspEpId());
         }
         if (MapUtil.isEmpty(sessionContext.getAfterFilterDesMap())) {
             log.debug("empty qualified dsp ep support.");
         }
         sessionContext.getAfterFilterDesMap().forEach((k, v) -> {
             for (DspEpSupportWrapper w : v) {
                 dspEpId.add(w.getDspEpId());
             }
         });
         if (CollUtil.isEmpty(dspEpId)) {
             log.info("no qualified dsp ep.");
             vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
             return;
         }
+        // 记录qps
+        DspEpQpsProvider dspEpQpsProvider = SpringContextHelper.getBean(DspEpQpsProvider.class);
+        dspEpQpsProvider.recordAfterIndexDspEpQps(sessionContext.getSspEp(), sessionContext.getUnifiedRequest(), dspEpId);
         // 发起请求
         DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
-        DspEpQpsProvider dspEpQpsProvider = SpringContextHelper.getBean(DspEpQpsProvider.class);
         QpsControlService qpsControlService = SpringContextHelper.getBean(QpsControlService.class);
         long maxTime = 0l;
         long currentTime = System.currentTimeMillis();
         String str = SpringContextHelper.getProperty("ssp.tmax.inner.handle-time");
         long handleTime = StrUtil.isBlank(str) ? 30L : Long.parseLong(str);
         long sspTmaxLeft = sessionContext.getUnifiedRequest().getTmax() - (currentTime - sessionContext.getStartTime());
+        if (sspTmaxLeft <= 0 || sspTmaxLeft < handleTime) {
+            log.warn("ssp tmax {} left is {}, set to {}.", sessionContext.getUnifiedRequest().getTmax(), sspTmaxLeft, 2 * handleTime);
+            sspTmaxLeft = 2 * handleTime;
+        }
         Map<Integer, DspEpObj> requestDspEpObj = new HashMap<>();
         Map<Integer, HttpClient> requestDspEpClient = new HashMap<>();
         for (Integer id : dspEpId) {
             DspEpObj dspEpObj = dspDataProvider.getDspEpObj(id);
             if (dspEpObj == null) {
                 log.info("dsp ep {} no config.", id);
                 continue;
             }
-            List<DspEpQpsProvider.KeyAndQps<String, Integer>> qpsList = dspEpQpsProvider.getDspEpQpsByReq(dspEpObj, sessionContext.getUnifiedRequest());
-            boolean subQpsFailed = false;
-            if (CollUtil.isNotEmpty(qpsList)) {
-                for (DspEpQpsProvider.KeyAndQps<String, Integer> keyAndQps : qpsList) {
-                    String key = keyAndQps.key();
-                    Integer qps = keyAndQps.qps();
-                    if (!qpsControlService.qpsCtrl(key, qps)) {
-                        log.info("dsp ep {} qps limit exceeded for key {}, skip.", id, key);
-                        markQpsFilterCount(dspEpObj);
-                        subQpsFailed = true;
-                        break;
-                    }
+            DspEpQps.KeyAndQps<String, Integer> subQps = dspEpQpsProvider.getDspEpQpsByReq(sessionContext.getSspEp(), dspEpObj, sessionContext.getUnifiedRequest());
+            if (subQps != null) {
+                if (subQps.qps() <= 0) {
+                    log.info("dsp ep {} sub qps {} is 0, skip.", id, subQps.key());
+                    markQpsFilterCount(dspEpObj);
+                    continue;
+                }
+                String key = subQps.key();
+                Integer qps = subQps.qps();
+                if (!qpsControlService.qpsCtrl(key, qps)) {
+                    log.info("dsp ep {} qps limit exceeded for key {}, skip.", id, key);
+                    markQpsFilterCount(dspEpObj);
+                    continue;
                 }
             }
-            if (subQpsFailed) {
-                continue;
-            }
+
             if (dspEpObj.getQps() > 0) {
                 if (!qpsControlService.qpsCtrl(dspEpObj.genQpsCtrlKey(), dspEpObj.getQps())) {
                     log.info("dsp ep {} qps limit exceeded, skip.", id);
                     markQpsFilterCount(dspEpObj);
                     continue;
                 }
             }
             requestDspEpObj.put(id, dspEpObj);
             HttpClient client = getDspHttpClient(dspEpObj);
             requestDspEpClient.put(id, client);
             long dspTmax = Math.min(sspTmaxLeft - handleTime, dspEpObj.getTimeout());
             dspEpObj.setTmax(dspTmax);
             maxTime = Math.max(dspTmax, maxTime);
         }
         long endTime = currentTime + maxTime;
         sessionContext.setRequestDspEndTime(endTime);
         sessionContext.setRequestSize(requestDspEpObj.size());
         // 加入监听
         TimeWheel timeWheel = SpringContextHelper.getBean(TimeWheel.class);
         timeWheel.addSessionContext(sessionContext);
+        // 记录真实请求QPS
+        dspEpQpsProvider.recordDspEpReqQps(sessionContext.getSspEp(), sessionContext.getUnifiedRequest(), requestDspEpObj.keySet());
         // 异步请求
         for (DspEpObj dspEpObj : requestDspEpObj.values()) {
             HttpClient client = requestDspEpClient.get(dspEpObj.getDspEpId());
             vertx.executeBlocking(new RequestDspCallable(vertx, sessionContext, client, dspEpObj), false).onComplete(result -> {
                 if (result.succeeded()) {
                     log.info("request to dsp {} ep {} success.", dspEpObj.getDspId(), dspEpObj.getDspEpId());
                 } else {
                     // 请求发送失败，标记完成
                     sessionContext.increaseCompletedRequests();
                     log.error("request to dsp {} ep {} failed, {}.", dspEpObj.getDspId(), dspEpObj.getDspEpId(), result.cause().getMessage(), result.cause());
                 }
             });
         }
     }
 
     HttpClient getDspHttpClient(DspEpObj dspEpObj) {
         if (dspClient.containsKey(dspEpObj.getDspId())) {
             return dspClient.get(dspEpObj.getDspId());
         } else {
             synchronized (RequestDspVerticle.class) {
                 if (dspClient.containsKey(dspEpObj.getDspId())) {
                     return dspClient.get(dspEpObj.getDspId());
                 }
                 HttpClientOptions options = new HttpClientOptions().setKeepAlive(true);
                 options.setTcpNoDelay(true);
                 String keepAliveTimeout = SpringContextHelper.getProperty("httpclient.keepalive.timeout");
                 String idleTimeout = SpringContextHelper.getProperty("httpclient.idle.timeout");
                 String cleanPeriod = SpringContextHelper.getProperty("httpclient.pool.clean.period");
                 String poolSize = SpringContextHelper.getProperty("httpclient.pool.max.poolSize");
                 String queueSize = SpringContextHelper.getProperty("httpclient.pool.max.queueSize");
                 options.setMaxPoolSize(Integer.parseInt(poolSize));
                 options.setMaxWaitQueueSize(Integer.parseInt(queueSize));
                 options.setKeepAliveTimeout(Integer.parseInt(keepAliveTimeout));
                 options.setPoolCleanerPeriod(Integer.parseInt(cleanPeriod));
                 options.setIdleTimeout(Integer.parseInt(idleTimeout));
                 options.setIdleTimeoutUnit(TimeUnit.SECONDS);
                 HttpClient client = vertx.createHttpClient(options);
 
                 dspClient.put(dspEpObj.getDspId(), client);
                 return client;
             }
         }
     }
 
     void markQpsFilterCount(DspEpObj dspObj) {
         String dspKey = "dsp." + dspObj.getPrefix() + ".qps.filter.count";
         if (dspQpsFilterCounter.containsKey(dspKey)) {
             dspQpsFilterCounter.get(dspKey).increment();
         } else {
             synchronized (RequestDspVerticle.class) {
                 if (dspQpsFilterCounter.containsKey(dspKey)) {
                     dspQpsFilterCounter.get(dspKey).increment();
                     return;
                 }
                 MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                 log.info("config dsp qps filter count: {}", dspKey);
                 Counter dsp = meterService.count(dspKey);
                 dsp.increment();
                 dspQpsFilterCounter.put(dspKey, dsp);
             }
         }
     }
 }
diff --git a/src/main/java/com/iflytek/traffic/util/qps/DimensionalQpsMonitorCache.java b/src/main/java/com/iflytek/traffic/util/qps/DimensionalQpsMonitorCache.java
new file mode 100644
index 0000000..c1f5a4b
--- /dev/null
+++ b/src/main/java/com/iflytek/traffic/util/qps/DimensionalQpsMonitorCache.java
@@ -0,0 +1,41 @@
+package com.iflytek.traffic.util.qps;
+
+import cn.hutool.core.util.StrUtil;
+import com.github.benmanes.caffeine.cache.Cache;
+import com.github.benmanes.caffeine.cache.Caffeine;
+import com.iflytek.traffic.util.SpringContextHelper;
+
+import java.util.HashMap;
+import java.util.Map;
+import java.util.concurrent.TimeUnit;
+
+public class DimensionalQpsMonitorCache {
+
+    // 使用Caffeine缓存维度组合计数器
+    private final Cache<String, QpsMonitor> qpsMonitorCache = Caffeine.newBuilder()
+//            .maximumSize(10_000) // 限制维度组合数量
+            .expireAfterAccess(5, TimeUnit.MINUTES) // 5分钟后过期
+            .build();
+
+
+    public void recordRequest(String key) {
+        String configStr = SpringContextHelper.getProperty("qps.monitor.window.size");
+        int windowSize = StrUtil.isNotBlank(configStr) && StrUtil.isNumeric(configStr) ? Integer.parseInt(configStr) : 100; // 默认窗口大小为100
+        QpsMonitor qpsMonitor = qpsMonitorCache.get(key, k -> new QpsMonitor(windowSize));
+        qpsMonitor.record();
+    }
+
+    // 获取指定维度的QPS
+    public double getQps(String key) {
+        QpsMonitor qpsMonitor = qpsMonitorCache.getIfPresent(key);
+        return qpsMonitor != null ? qpsMonitor.getQps() : 0;
+    }
+
+    public Map<String, Double> getAllQps() {
+        Map<String, Double> allQps = new HashMap<>();
+        qpsMonitorCache.asMap().forEach((key, monitor) -> {
+            allQps.put(key, monitor.getQps());
+        });
+        return allQps;
+    }
+}
diff --git a/src/main/java/com/iflytek/traffic/util/qps/QpsMonitor.java b/src/main/java/com/iflytek/traffic/util/qps/QpsMonitor.java
new file mode 100644
index 0000000..3222bcb
--- /dev/null
+++ b/src/main/java/com/iflytek/traffic/util/qps/QpsMonitor.java
@@ -0,0 +1,87 @@
+package com.iflytek.traffic.util.qps;
+
+import com.codahale.metrics.Reservoir;
+import com.codahale.metrics.SlidingTimeWindowReservoir;
+import com.codahale.metrics.Snapshot;
+import com.iflytek.traffic.service.NacosEventListener;
+import com.iflytek.traffic.util.SpringContextHelper;
+import lombok.extern.slf4j.Slf4j;
+
+import java.util.concurrent.TimeUnit;
+
+/**
+ * 基于 Dropwizard Metrics的SlidingTimeWindowReservoir滑动窗口QPS统计
+ */
+@Slf4j
+public class QpsMonitor {
+    // 计时器
+    private final Reservoir reservoir;
+    // 时间窗口大小和单位
+    private final long windowSize;
+    private final TimeUnit timeUnit = TimeUnit.SECONDS;
+
+    /**
+     * 创建QPS监控器
+     *
+     * @param windowSize 时间窗口大小，单位为秒
+     */
+    public QpsMonitor(long windowSize) {
+        this.windowSize = windowSize;
+        this.reservoir = new SlidingTimeWindowReservoir(windowSize, timeUnit);
+    }
+
+    public void record() {
+        reservoir.update(System.nanoTime());
+    }
+
+    /**
+     * 获取当前 QPS
+     */
+    public double getQps() {
+        Snapshot snapshot = reservoir.getSnapshot();
+        long eventCount = snapshot.size();
+
+        // 计算实际时间跨度（纳秒）
+        long actualWindow = calculateActualWindow(snapshot);
+
+        // 转换为秒
+        double seconds = TimeUnit.NANOSECONDS.toSeconds(actualWindow);
+
+        // 防止除零错误
+        double  qps = seconds > 0 ? eventCount / seconds : 0.0;
+        NacosEventListener nacos = SpringContextHelper.getBean(NacosEventListener.class);
+        if (nacos != null) {
+            int serviceCount = nacos.getServiceCount();
+            if (serviceCount > 0) {
+                qps *= serviceCount;
+            }
+        } else {
+            log.warn("NacosEventListener bean not found, using default service count of 1.");
+        }
+        return qps;
+    }
+
+    // 计算实际时间跨度
+    private long calculateActualWindow(Snapshot snapshot) {
+        if (snapshot.size() == 0) {
+            return timeUnit.toNanos(windowSize);
+        }
+
+        // 获取时间窗口配置
+        long configuredWindow = timeUnit.toNanos(windowSize);
+
+        if (snapshot.size() == 1) {
+            return Math.min(System.nanoTime() - snapshot.getValues()[0], configuredWindow);
+        }
+        // 获取实际最小和最大值
+        long min = Long.MAX_VALUE;
+        long max = Long.MIN_VALUE;
+        for (long value : snapshot.getValues()) {
+            if (value < min) min = value;
+            if (value > max) max = value;
+        }
+
+        // 实际时间跨度不超过配置的时间窗口
+        return Math.min(max - min, configuredWindow);
+    }
+}
