<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="OFF" monitorinterval="5">
    <Properties>
        <property name="log_pattern_default">%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%t] [%logger] - %msg%n</property>
        <property name="log_dir_default">logs</property>
        <property name="log_pattern_event">%d{yyyy-MM-dd-HH:mm:ss.SSS} %msg%n</property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${log_pattern_default}" disableAnsi="false" noConsoleNoAnsi="false"/>
        </Console>

        <RollingRandomAccessFile name="DefaultAppender" fileName="${log_dir_default}/traffic-server.log"
                                 filePattern="${log_dir_default}/archive/%d{yyyy-MM-dd}/traffic-server%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true"
                                 append="true">
            <PatternLayout pattern="${log_pattern_default}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="500MB"/>
            </Policies>
            <DefaultRolloverStrategy max="128">
                <Delete basePath="${log_dir_default}/archive" maxDepth="2">
                    <IfFileName glob="*/traffic-server*.log"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="AbroadAdxTraceAppender"
                                 fileName="${log_dir_default}/trace/trace.log"
                                 filePattern="${log_dir_default}/trace/trace_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/trace" maxDepth="2">
                    <IfFileName glob="trace*.log"/>
                    <IfLastModified age="4h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="AbroadAdxNoBidAppender"
                                 fileName="${log_dir_default}/no_bid/no_bid.log"
                                 filePattern="${log_dir_default}/no_bid/no_bid_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/no_bid" maxDepth="2">
                    <IfFileName glob="no_bid*.log"/>
                    <IfLastModified age="4h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="FilebeatAbroadAdxTraceAppender"
                                 fileName="${log_dir_default}/filebeat_trace/filebeat_trace.log"
                                 filePattern="${log_dir_default}/filebeat_trace/filebeat_trace_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/filebeat_trace" maxDepth="2">
                    <IfFileName glob="filebeat_trace*.log"/>
                    <IfLastModified age="1h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="MaterialLogAppender"
                                 fileName="${log_dir_default}/material/material.log"
                                 filePattern="${log_dir_default}/material/material_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/material" maxDepth="2">
                    <IfFileName glob="material*.log"/>
                    <IfLastModified age="4h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="AdmVerifyLogAppender"
                                 fileName="${log_dir_default}/adm_verify/adm_verify.log"
                                 filePattern="${log_dir_default}/adm_verify/adm_verify_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/adm_verify" maxDepth="2">
                    <IfFileName glob="adm_verify*.log"/>
                    <IfLastModified age="4h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="AbroadSessionSampleAppender"
                                 fileName="${log_dir_default}/sample/sample.log"
                                 filePattern="${log_dir_default}/sample/sample_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/sample" maxDepth="2">
                    <IfFileName glob="sample*.log"/>
                    <IfLastModified age="4h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="AbroadAdxRetrievalAppender"
                                 fileName="${log_dir_default}/retrieval/retrieval.log"
                                 filePattern="${log_dir_default}/retrieval/retrieval_%d{yyyy-MM-dd-HH}_%i.log"
                                 immediateFlush="true" append="true" ignoreExceptions="false">
            <PatternLayout pattern="${log_pattern_event}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="4096MB"/>
            </Policies>
            <DefaultRolloverStrategy max="256">
                <Delete basePath="${log_dir_default}/retrieval" maxDepth="2">
                    <IfFileName glob="retrieval*.log"/>
                    <IfLastModified age="1h"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

    </Appenders>
    <Loggers>
        <Root level="WARN">
            <AppenderRef ref="DefaultAppender"/>
        </Root>

        <logger name="abroad_adx_trace_log" level="DEBUG" additivity="false">
            <AppenderRef ref="AbroadAdxTraceAppender"/>
        </logger>
        <logger name="abroad_adx_no_bid_log" level="DEBUG" additivity="false">
            <AppenderRef ref="AbroadAdxNoBidAppender"/>
        </logger>

        <logger name="material_log" level="DEBUG" additivity="false">
            <AppenderRef ref="MaterialLogAppender"/>
        </logger>

        <logger name="adm_verify_log" level="INFO" additivity="false">
            <AppenderRef ref="AdmVerifyLogAppender"/>
        </logger>

        <logger name="filebeat_abroad_adx_trace_log" level="DEBUG" additivity="false">
            <AppenderRef ref="FilebeatAbroadAdxTraceAppender"/>
        </logger>

        <logger name="abroad_session_sample_log" level="INFO" additivity="false">
            <AppenderRef ref="AbroadSessionSampleAppender"/>
        </logger>

        <logger name="abroad_adx_retrieval_log" level="INFO" additivity="false">
            <AppenderRef ref="AbroadAdxRetrievalAppender"/>
        </logger>

    </Loggers>
</Configuration>