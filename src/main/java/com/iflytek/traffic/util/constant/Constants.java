package com.iflytek.traffic.util.constant;

/**
 * <AUTHOR>
 * @datetime 2025/5/9 13:57
 */
public class Constants {
	public static final int PRICE_MULTIPLY_MILLIONS = 1000000;

	public static final int COST_PER_MILLE = 1000;

	/* ip2location未查到数据*/
	public static final String NOT_SUPPORTED = "Not_Supported";
	/* 结算类型：1:adm；2:burl */
	public static final int SETTLEMENTTYPE_BURL = 2;

	/* native 图片类型：主图 */
	public static final int NATIVE_IMG_TYPE_IMG = 3;
	/* native 图片类型：icon */
	public static final int NATIVE_IMG_TYPE_ICON = 1;

	public static final int AD_TYPE_ICON = 10;

	/* 过滤原因：出价 */
	public static final int FILTER_REASON_PRICE = 1;
	/* 过滤原因：素材 */
	public static final int FILTER_REASON_CREATIVE = 2;
	/* 过滤原因：请求响应转换异常 */
	public static final int FILTER_REASON_CONVERT = 3;
	/* 过滤原因：策略 */
	public static final int FILTER_REASON_STRATEGY = 4;
	/* 过滤原因：缺少信息 */
	public static final int FILTER_REASON_MISS_INFO = 5;
	/* 过滤原因：黑名单 */
	public static final int FILTER_REASON_BLACKLIST = 6;

	public static final int T_MAX_DEFAULT = 6000000;
}
