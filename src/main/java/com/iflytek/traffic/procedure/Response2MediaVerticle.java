package com.iflytek.traffic.procedure;

import com.iflytek.traffic.procedure.callable.ResponseToMediaCallable;
import com.iflytek.traffic.session.SessionContext;
import io.vertx.core.Future;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EventListener(listen = EventBusAddress.RESPONSE_TO_MEDIA)
public class Response2MediaVerticle extends BasicVerticle {

    void doHandle(SessionContext sessionContext) {
        Future<Boolean> res = vertx.executeBlocking(new ResponseToMediaCallable(vertx, sessionContext), false);
        res.onComplete(result -> {
            if (result.succeeded()) {
                log.info("response to media success.");
            } else {
                log.info("response to media failed, {}.", result.cause().getMessage(), result.cause());
            }
        });
    }

}
