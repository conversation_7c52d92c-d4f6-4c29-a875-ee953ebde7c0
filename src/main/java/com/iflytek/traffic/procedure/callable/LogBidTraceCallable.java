package com.iflytek.traffic.procedure.callable;

import com.iflytek.traffic.log.LogService;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.SpringContextHelper;
import io.micrometer.core.instrument.Timer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @datetime 2025/5/12 9:57
 */
@AllArgsConstructor
@Slf4j
public class LogBidTraceCallable implements Callable<Boolean> {

	private long start;

	private SessionContext sessionContext;

	private static Timer logTimer;

	@Override
	public Boolean call() throws Exception {
		try {
			LogService logService = SpringContextHelper.getBean(LogService.class);

			// 记录日志
			logService.logAdxTrace(sessionContext);
			logService.logRetrievalLog(sessionContext);
			if (getLogTimer() != null) {
				getLogTimer().record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
			}
		} catch (Exception e) {
			log.error("log trace error: {}", e.getMessage(), e);
		}
		return true;
	}

	Timer getLogTimer() {
		if (logTimer != null) {
			return logTimer;
		}
		MeterService meterService = SpringContextHelper.getBean(MeterService.class);
		String key = "log.trace.time.total";
		log.debug("config log trace timer: {}", key);
		logTimer = meterService.time(key);
		return logTimer;
	}
}
