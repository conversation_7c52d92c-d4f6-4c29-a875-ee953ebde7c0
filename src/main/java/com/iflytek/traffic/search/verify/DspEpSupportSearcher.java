package com.iflytek.traffic.search.verify;

import cn.hutool.core.collection.ListUtil;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.SpringContextHelper;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
@Service
public class DspEpSupportSearcher implements InitializingBean {

    @Autowired
    private FloorPriceVerifier floorPriceVerifier;

    @Autowired
    private MeterService meterService;

    private ArrayList<BasicVerifier> verifiers = new ArrayList<>();

    private Map<String, Timer> verifierTimerMap = new ConcurrentHashMap<>();


    @Override
    public void afterPropertiesSet() throws Exception {
        addVerifier(floorPriceVerifier);
    }

    protected void addVerifier(BasicVerifier verifier) {
        this.verifiers.add(verifier);
        String vName = verifier.getClass().getSimpleName();
        Timer timer = meterService.time("dse.vt." + vName);
        this.verifierTimerMap.put(vName, timer);
    }

    public void search(List<DspEpSupportWrapper> wrapperList, SessionContext sessionContext) {
        for (BasicVerifier verifier : verifiers) {
            long vStart = System.currentTimeMillis();
            String vName = verifier.getClass().getSimpleName();
            verifier.filter(wrapperList, sessionContext);
            Timer timer = this.verifierTimerMap.get(vName);
            timer.record(System.currentTimeMillis() - vStart, TimeUnit.MILLISECONDS);
        }
    }
}
