package com.iflytek.traffic.search.index;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Sets;
import com.iflytek.traffic.data.entity.EpSupport;
import com.iflytek.traffic.data.entity.EpSupportItem;
import com.iflytek.traffic.data.provider.DspDataProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.dsp.DspEpSupport;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.search.verify.DspEpSupportSearcher;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DspEpSupportIndex extends BaseIndex<DspEpSupport, DspEpSupportWrapper> {

    @Autowired
    private DspDataProvider dspDataProvider;

    @Autowired
    private DspEpSupportSearcher dspEpSupportSearcher;

    public void fresh(Map<Integer, EpSupport> epSupportMap) {
        // 先删除不生效的圈量策略
        Set<Integer> keyNew = epSupportMap.keySet();
        Set<Integer> keyOld = this.id2ObjMap.keySet();
        Set<Integer> keyDel = Sets.difference(keyOld, keyNew);
        for (Integer id : keyDel) {
            del(id);
        }
        // 再添加新的圈量策略
        epSupportMap.forEach((desId, epSupport) -> {
            DspEpSupport des = new DspEpSupport();
            des.setId(desId);
            des.setDspEpSupportId(epSupport.getSupportId());
            des.setDspEpId(epSupport.getDspEpId());
            DspEpObj dspEpObj = dspDataProvider.getDspEpObj(epSupport.getDspEpId());
            if (dspEpObj == null) {
                log.warn("DspEpObj for dspEpId {} not found, skip support {}", epSupport.getDspEpId(), desId);
                return;
            }
            des.setDspId(dspEpObj.getDspId());
            des.setDspName(dspEpObj.getName());
            List<Long> in = new ArrayList<>();
            List<Long> out = new ArrayList<>();
            int cjHeadNum = 0;
            for (EpSupportItem item : epSupport.getSupportItemList()) {
                if (item.getConfigId() == 1011 && CollUtil.isNotEmpty(item.getFpDirects())) {
                    // 底价定向
                    des.setFpDirects(item.getFpDirects());
                    continue;
                }
                if (CollUtil.isEmpty(item.getConfigItemList())) {
                    log.debug("support {} config {} item is empty, skip", desId, item.getConfigId());
                    continue;
                }
                if (item.getInclude().equals(2)) {
                    // 排除
                    for (String config : item.getConfigItemList()) {
                        Long dnf = Util.GenDNFByStr(getHead(item.getConfigId()), config);
                        out.add(dnf);
                    }
                    continue;
                }
                String head = getHead(item.getConfigId());
                cjHeadNum++;
                for (String config : item.getConfigItemList()) {
                    Long dnf = Util.GenDNFByStr(head, config);
                    in.add(dnf);
                }
            }
            des.setIn(in);
            des.setOut(out);
            des.setCjHeadNum(cjHeadNum);
            add(des);
        });
    }

    private String getHead(Integer configId) {
        return switch (configId) {
            case 1001 ->
                // ssp
                    "ss";
            case 1002 ->
                // ssp ep
                    "se";
            case 1003 ->
                // 包名
                    "pk";
            case 1004 ->
                // 地域
                    "rg";
            case 1005 ->
                // 广告形式
                    "st";
            case 1006 ->
                // 操作系统
                    "os";
            case 1007 ->
                // displayManager
                    "dm";
            case 1008 ->
                // supplier chain
                    "sc";
            case 1009 ->
                // app分类
                    "ac";
            case 1010 ->
                // 尺寸
                    "hw";
            default -> "";
        };
    }

    @Override
    public void add(DspEpSupport dspEpSupport) {
        // 先删除，再构建新的索引关系
        del(dspEpSupport.getId());
        this.id2ObjMap.put(dspEpSupport.getId(), dspEpSupport);
        for (Long in : dspEpSupport.getIn()) {
            this.incj2IdMap.computeIfAbsent(in, k -> new HashSet<>()).add(dspEpSupport.getId());
        }
        for (Long out : dspEpSupport.getOut()) {
            this.outcj2IdMap.computeIfAbsent(out, k -> new HashSet<>()).add(dspEpSupport.getId());
        }
    }

    @Override
    public void del(Integer id) {
        if (!this.id2ObjMap.containsKey(id)) {
            return;
        }
        DspEpSupport des = this.id2ObjMap.remove(id);
        for (Long in : des.getIn()) {
            if (this.incj2IdMap.containsKey(in)) {
                this.incj2IdMap.get(in).remove(id);
            }
        }
        for (Long out : des.getOut()) {
            if (this.outcj2IdMap.containsKey(out)) {
                this.outcj2IdMap.get(out).remove(id);
            }
        }
    }

    @Override
    public List<DspEpSupportWrapper> pickAllObj(List<Long> reqDnf, List<Long> impDnf, SessionContext sessionContext) {
        List<Long> allDnf = new ArrayList<>();
        allDnf.addAll(reqDnf);
        allDnf.addAll(impDnf);
        if (CollUtil.isEmpty(allDnf)) {
            return List.of();
        }
        Map<Integer, Set<Long>> desId2cjHeadMap = new HashMap<>();
        Map<Integer, Set<Long>> desIdBan2cjHeadMap = new HashMap<>();
        for (Long dnf : allDnf) {
            Long head = dnf >>> 48;
            Set<Integer> desIds = this.incj2IdMap.get(dnf);
            if (CollUtil.isNotEmpty(desIds)) {
                for (Integer desId : desIds) {
                    desId2cjHeadMap.computeIfAbsent(desId, k -> new HashSet<>()).add(head);
                }
            }
            Set<Integer> banDesIds = this.outcj2IdMap.get(dnf);
            if (CollUtil.isNotEmpty(banDesIds)) {
                for (Integer banDesId : banDesIds) {
                    desIdBan2cjHeadMap.computeIfAbsent(banDesId, k -> new HashSet<>()).add(head);
                }
            }
        }
        List<DspEpSupportWrapper> qualifiedDspEp = new ArrayList<>();
        if (MapUtil.isNotEmpty(desId2cjHeadMap)) {
            for (Integer desId : desId2cjHeadMap.keySet()) {
                if (desIdBan2cjHeadMap.containsKey(desId)) {
                    continue;
                }
                DspEpSupport des = this.id2ObjMap.get(desId);
                if (des != null) {
                    Set<Long> cjHeads = desId2cjHeadMap.get(desId);
                    if (des.getCjHeadNum() <= cjHeads.size()) {
                        qualifiedDspEp.add(new DspEpSupportWrapper(des));
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(qualifiedDspEp)) {
            dspEpSupportSearcher.search(qualifiedDspEp, sessionContext);
        }
        return qualifiedDspEp;
    }

    public Set<Integer> getAllConfigDspEpId() {
        return this.id2ObjMap.values().stream().map(DspEpSupport::getDspEpId).collect(Collectors.toSet());
    }
}
