package com.iflytek.traffic.session;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.protocol.AllProtocolConfig;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.service.TimeWheel;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.SpringContextHelper;
import com.iflytek.traffic.util.Util;
import jakarta.servlet.AsyncContext;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
@Slf4j
public class SessionContext {

    private String sessionId = UUID.fastUUID().toString();

    private Long startTime = System.currentTimeMillis();

    private List<Long> eachStepExecuteTime = new ArrayList<>();

    private SspEp sspEp;

    private byte[] reqBody;

    // 由ssp请求转换来的内部请求
    private UnifiedRequest unifiedRequest;

    // 通过索引的圈量策略
    private Map<String, List<DspEpSupportWrapper>> impId2DesMap = new HashMap<>();

    // 筛选后的圈量策略
    private Map<String, List<DspEpSupportWrapper>> afterFilterDesMap = new HashMap<>();

    // 未配置圈量策略的dsp ep
    private List<Integer> nonConfigDspEpId = new ArrayList<>();

    // DSP 对应的融量配置
    private Map<Integer, Map<String, FusionProvider.TargetTraffic>> dspFusionTarget = new HashMap<>();

    private volatile long requestDspEndTime = 0l;

    private volatile Integer timeWheelSlot;

    private long timeWheelStartTime = 0l;

    private volatile int requestSize = 0;

    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private AtomicInteger completedRequests = new AtomicInteger(0);

    private Map<Integer, DspEpObj> requestSucDspEp = new HashMap<>();

    private Map<Integer, byte[]> dspReqBody = new HashMap<>();

    private Map<Integer, UnifiedResponse> dspUnifiedResp = new HashMap<>();
    // 成功响应的dsp ep信息
    private Map<Integer, DspEpObj> responseDspEpObj = new HashMap<>();

    private Map<Integer, byte[]> dspRespBody = new HashMap<>();

    private Integer winner;

    private UnifiedResponse winUResp;

    private boolean asyncContextCompleted = false;

    private AsyncContext asyncContext;

    // 最终返回给媒体的dealid
    private String bidDealId;

    private int respStatus = 204;

    private String respContentType = "text/plain";

    private byte[] respBody;

    public ProtocolParser getProtocolParser(String protocol) {
        AllProtocolConfig allProtocolConfig = SpringContextHelper.getBean(AllProtocolConfig.class);
        if (allProtocolConfig == null) {
            log.error("bean: AllProtocolConfig is null");
            return null;
        }
        ProtocolParser result = allProtocolConfig.getProtocolParser(protocol);
        if (result == null) {
            log.warn("no protocol: {}", protocol);
        }
        return result;
    }

    private String startTime4Log;

    public Object getStartTime4Log() {
        if (startTime4Log == null) {
            startTime4Log = Util.timestamp4Log(startTime);
        }
        return startTime4Log;
    }

    public Long getRegion() {
        if (unifiedRequest != null &&
                unifiedRequest.getDevice() != null &&
                unifiedRequest.getDevice().getRegionInfo() != null) {
            return unifiedRequest.getDevice().getRegionInfo().getRegion();
        }

        return null;
    }

    public void addExecuteTime(Long time) {
        if (time != null) {
            eachStepExecuteTime.add(time);
        }
    }

    public Long getLastTwoStepTimeGap() {
        if (eachStepExecuteTime.size() < 2) {
            return 0l;
        }
        int size = eachStepExecuteTime.size();
        return eachStepExecuteTime.get(size - 1) - eachStepExecuteTime.get(size - 2);
    }

    public void increaseCompletedRequests() {
        int completeSize = completedRequests.incrementAndGet();
        if (timeWheelSlot == null) {
            log.info("time wheel slot is not set, sessionId: {}", sessionId);
            return;
        }
        if (System.currentTimeMillis() >= requestDspEndTime || completeSize >= requestSize) {
            TimeWheel timeWheel = SpringContextHelper.getBean(TimeWheel.class);
            timeWheel.finishSessionContext(this);
        }
    }

    public int getCompletedRequests() {
        return completedRequests.get();
    }

    public boolean logSample() {
        String rateStr = SpringContextHelper.getProperty("log.session.sample.rate");
        int sampleRate = StrUtil.isNotBlank(rateStr) ? Integer.parseInt(rateStr) : 100;
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int r = random.nextInt(10000);
        return r < sampleRate;
    }
}
