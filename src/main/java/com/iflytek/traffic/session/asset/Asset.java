package com.iflytek.traffic.session.asset;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2025/5/8 15:34
 */
public class Asset {
	/**
	 * 广告类型
	 */
	public enum AdType {
		UNKNOWN(0), // 未知
		SUPPER_BANNER(100), // 超级BANNER
		SUPPER_SPLASH(200), // 超级开屏
		SUPPER_NATIVE(300), // 超级信息流
		SUPPER_VIDEO(400), // 超级视频贴片
		SUPPER_REWARD_VIDEO(401), // 激励视频
		SUPPER_NON_REWARD_VIDEO(402), // 非激励视频
		//SUPPER_VIDEO_INCENTIVE(550), // 超级激励视频
//		SUPPER_AUDIO(570), // 超级音频贴片
		SUPPER_ICON(500),
		SUPPER_PUSH(600),
		SUPPER_INSTL(700);


		private final int value;

		static Map<Integer, AdType> integerMap = new HashMap<>();

		static {
			for (AdType adType : AdType.values()) {
				integerMap.put(adType.getValue(), adType);
			}
		}

		// 构造方法必须是private或者默认
		private AdType(int value) {
			this.value = value;
		}

		public static AdType getAdType(int adType) {
			if (integerMap.containsKey(adType)) {
				return integerMap.get(adType);
			}
			return AdType.UNKNOWN;
		}

		public int getValue() {
			return this.value;
		}

		public String toString() {
			return String.valueOf(this.value);
		}
	}
}
