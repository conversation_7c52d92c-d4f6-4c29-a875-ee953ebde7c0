package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.entity.DspEpInfo;
import com.iflytek.traffic.data.mapper.info.DspMapper;
import com.iflytek.traffic.dsp.DspEpObj;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DspDataProvider {


    @Autowired
    private DspMapper dspMapper;

    public Map<Integer, DspEpInfo> dspEpInfoMap = new HashMap<>();


    @Scheduled(fixedRateString = "${data.update.interval.dsp:300000}")
    public void update(){
        updateDspEpInfo();
    }

    /**
     * 定时刷新ep信息
     */
    public void updateDspEpInfo(){
        List<DspEpInfo> dspEpInfoList = dspMapper.selectValidEp();
        if (CollectionUtil.isEmpty(dspEpInfoList)) {
            log.error("update with empty dsp ep infos");
            return;
        }
        log.info("update dsp ep info, size: {}", dspEpInfoList.size());
        Iterator<DspEpInfo> iterator = dspEpInfoList.iterator();
        while (iterator.hasNext()) {
            DspEpInfo dspEpInfo = iterator.next();
            if (StrUtil.isBlank(dspEpInfo.getPrefix()) || dspEpInfo.getPrefix().equals("unknown")) {
                log.warn("dsp ep path is blank or path is unknown: {}", dspEpInfo.getId());
                iterator.remove();
            }
            if (StrUtil.isBlank(dspEpInfo.getProtocol())) {
                log.warn("dsp ep protocol is blank: {}", dspEpInfo.getId());
                iterator.remove();
            }
            if (StrUtil.isBlank(dspEpInfo.getPath())) {
                log.warn("dsp ep path is blank: {}", dspEpInfo.getId());
                iterator.remove();
            }
        }
        Map<Integer, DspEpInfo> tmp = dspEpInfoList.stream().collect(Collectors.toMap(DspEpInfo::getId, Function.identity()));
        dspEpInfoMap = tmp;
    }

    public DspEpObj getDspEpObj(Integer dspEpId) {
        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
            DspEpObj dspEpObj = new DspEpObj();
            DspEpInfo info = dspEpInfoMap.get(dspEpId);
            dspEpObj.setDspId(info.getDspId());
            dspEpObj.setDspEpId(info.getId());
            dspEpObj.setQps(Long.valueOf(info.getQps()).intValue());
            dspEpObj.setPath(info.getPath());
            dspEpObj.setPrefix(info.getPrefix());
            dspEpObj.setName(info.getDspName());
            dspEpObj.setTimeout(info.getTimeout());
            dspEpObj.setIsGzip(info.getIsGzip());
            dspEpObj.setSettlementType(info.getSettlementType());
            dspEpObj.setProtocol(info.getProtocol());
            dspEpObj.setImpTtl(info.getDspImpTimeout());
            if (info.getImpTimeout() != null && info.getImpTimeout() > 0) {
                dspEpObj.setImpTtl(info.getImpTimeout());
            }
            return dspEpObj;
        }
        return null;
    }

    public List<Integer> diffDspEpId(Set<Integer> dspEpId) {
        if (CollUtil.isEmpty(dspEpId)) {
            return CollUtil.newArrayList(dspEpInfoMap.keySet());
        }
        List<Integer> res = CollUtil.newArrayList();
        for (Integer id : dspEpInfoMap.keySet()) {
            if (!dspEpId.contains(id)) {
                res.add(id);
            }
        }
        return res;
    }

    public Integer getDspId(Integer dspEpId) {
        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
            DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
            return dspEpInfo.getDspId();
        }
        return null;
    }

    public Integer getDspEpQps(Integer dspEpId) {
        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
            DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
            return Long.valueOf(dspEpInfo.getQps()).intValue();
        }
        return null;
    }

}
