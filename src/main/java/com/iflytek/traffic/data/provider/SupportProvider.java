package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.traffic.data.entity.*;
import com.iflytek.traffic.data.mapper.info.SupportMapper;
import com.iflytek.traffic.search.index.DspEpSupportIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupportProvider {

    @Autowired
    private SupportMapper supportMapper;

    @Autowired
    private DspEpSupportIndex dspEpSupportIndex;


    @Scheduled(fixedRateString = "${data.update.interval.dsp:300000}")
    public void update() {
        updateSupport();
    }

    public void updateSupport() {
        List<EpSupportInfo> epSupportInfoList = supportMapper.selectValidSupport();
        Map<Integer, List<EpSupportInfo>> id2InfoMap = epSupportInfoList.stream().collect(Collectors.groupingBy(EpSupportInfo::getId));
        Map<Integer, EpSupport> epSupportMap = new HashMap<>();
        id2InfoMap.forEach((id, supportInfoList) -> {
            EpSupport epSupport = new EpSupport();
            epSupport.setId(id);
            EpSupportInfo esi = supportInfoList.get(0);
            epSupport.setSupportId(esi.getSupportId());
            epSupport.setDspEpId(esi.getEpId());
            List<EpSupportItem> supportItemList = new ArrayList<>();
            supportInfoList.forEach(s -> {
                if (StrUtil.isBlank(s.getConfigValue())
                        || s.getConfigValue().equals("{}")
                        || s.getConfigValue().equals("[]")) {
                    log.debug("support {} config {} value is empty, skip", id, s.getConfigId());
                    return;
                }
                EpSupportItem item = new EpSupportItem();
                item.setConfigId(s.getConfigId());
                item.setInclude(s.getInclude());
                //包名, DisplayManager、尺寸特殊处理
                if (s.getConfigId() == 1003 || s.getConfigId() == 1007 || s.getConfigId() == 1010) {
                    List<String> configItemList = Arrays.asList(s.getConfigValue().split("\n"));
                    item.setConfigItemList(configItemList);
                } else if (s.getConfigId() == 1008) {
                    // supplier chain
                    SupplierChainDirect direct = JSONObject.parseObject(s.getConfigValue(), SupplierChainDirect.class);
                    List<String> sspNameList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(direct.getSsps())) {
                        sspNameList.addAll(direct.getSsps());
                    } else {
                        sspNameList.add("*");
                    }
                    List<Integer> sspLevelList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(direct.getSupplierChains())) {
                        sspLevelList.addAll(direct.getSupplierChains());
                    } else {
                        sspLevelList.add(-1);
                    }
                    List<String> configItems = new ArrayList<>();
                    for (String sspName : sspNameList) {
                       for (Integer level : sspLevelList) {
                           configItems.add(sspName + "_" + level);
                       }
                    }
                    item.setConfigItemList(configItems);
                } else if (s.getConfigId() == 1011) {
                    // 底价
                    List<FloorPriceDirect> fpDirect = JSONArray.parseArray(s.getConfigValue(), FloorPriceDirect.class);
                    item.setFpDirects(fpDirect);
                } else {
                    List<String> configItemList = JSONArray.parseArray(s.getConfigValue(), String.class);
                    item.setConfigItemList(configItemList);
                }
                supportItemList.add(item);
            });
            epSupport.setSupportItemList(supportItemList);
            epSupportMap.put(id, epSupport);
        });
        // TODO 加入索引
        dspEpSupportIndex.fresh(epSupportMap);
    }

}
