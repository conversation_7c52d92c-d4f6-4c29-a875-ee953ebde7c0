package com.iflytek.traffic.data.entity;

import lombok.Data;

@Data
public class DspEpInfo {
    private Integer id;
    /**
     * dsp_id对应d_dsp表id
     */
    private Integer dspId;

    /**
     * EP节点ID：0：全部国家；1:东南亚；2:美洲；3:欧洲
     */
    private Integer nodeId;

    /**
     * 请求路径
     */
    private String path;

    /**
     * DSP 请求QPS限制
     */
    private long qps;

    /**
     * EP的可使用状态，1表示可使用，2表示不可使用，
     */
    private Integer epStatus;

    /**
     * 是否被删除，0：正常，1：已删除
     */
    private Integer isDel;

    /**
     * DSP平台名称
     */
    private String dspName;

    /**
     * 对接状态：1:测试；2:正式投放
     */
    private Integer dockingType;

    /**
     * 币种ID，1：美元；2:人民币
     */
    private Integer currencyId;

    /**
     * 是否支持 gzip：0:不支持；1:支持
     */
    private Integer isGzip;

    /**
     * 是否支持 pmp，0:不支持1；1:支持
     */
    private Integer isPmp;

    /**
     * 结算类型：1:adm；2:burl
     */
    private Integer settlementType;

    /**
     * DSP请求超时时间，单位毫秒
     */
    private Integer timeout;

    /**
     * DSP曝光有效期，单位秒
     */
    private Integer dspImpTimeout;

    /**
     * DSP EP曝光有效期，单位秒
     */
    private Integer impTimeout;

    /**
     * 平台的可状态：1表示可使用，2表示不可使用
     */
    private Integer dspStatus;

    /**
     * 协议
     */
    private String protocol;

    /**
     * 前缀，用于区分dsp ep
     */
    private String prefix;
}
