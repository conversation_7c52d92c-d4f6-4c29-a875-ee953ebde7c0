# QPS缺口计算应该基于reqQps的技术论证

## 核心观点

在DSP流量精细化调控系统中，QPS缺口(Gap)的计算应该基于 `reqQps < targetQps` 而不是 `sspQps < targetQps`。本文通过多个真实场景案例来论证这一观点的正确性。

## 关键概念理解

- **targetQps**: 运营人员配置的目标QPS，代表每个维度组合应该获得的流量分配
- **reqQps**: 当前实际发送给DSP的QPS，代表当前的分配结果  
- **sspQps**: SSP侧满足圈量规则的流量供给能力，代表理论上限

## 核心理念：分配公平性 vs 供给能力

QPS调控系统的本质目标是**确保每个维度组合都能按照配置的目标获得合理的流量分配**，这是一个"分配公平性"问题，而不是"供给能力"问题。

## 场景案例分析

### 案例1：标准的流量重分配场景

**背景**: DSP EP总QPS=1000，配置比例 Banner=40%, Video=60%

**维度数据**:
- **Banner维度**: targetQps=400, reqQps=250, sspQps=600, 填充率=95%
- **Video维度**: targetQps=600, reqQps=750, sspQps=800, 填充率=75%

**按reqQps逻辑分析**:
```java
Banner: reqQps(250) < targetQps(400) → gap = 150 (分配不足)
Video: reqQps(750) > targetQps(600) → 超额 = 150 (过度分配)
重分配: Video减少150给Banner，最终Banner=400, Video=600
```

**按sspQps逻辑分析**:
```java
Banner: sspQps(600) > targetQps(400) → 无gap (供给充足)
Video: sspQps(800) > targetQps(600) → 无gap (供给充足)  
结果: 无需调整，Banner永远只能得到250 (仅为目标的62.5%)
```

**结论**: Banner维度有95%的高填充率和充足供给，但只获得了目标的62.5%，这明显不合理。reqQps逻辑能够纠正这种分配不公。

### 案例2：新维度的流量获取

**背景**: 新上线的高价值维度需要获得合理的流量分配

**维度数据**:
- **老维度A**: targetQps=300, reqQps=380, sspQps=450, 填充率=80%
- **新维度B**: targetQps=300, reqQps=80, sspQps=500, 填充率=98%

**按reqQps逻辑分析**:
```java
老维度A: reqQps(380) > targetQps(300) → 超额 = 80
新维度B: reqQps(80) < targetQps(300) → gap = 220
重分配: A减少80给B，B从其他维度再获得140，逐步接近目标
```

**按sspQps逻辑分析**:
```java
老维度A: sspQps(450) > targetQps(300) → 无gap
新维度B: sspQps(500) > targetQps(300) → 无gap
结果: B永远只能得到80的QPS，无法发挥98%填充率的价值
```

**结论**: 新维度B有极高的填充率和充足供给，但当前分配严重不足。sspQps逻辑会阻止高价值维度获得应有的流量。

### 案例3：配置比例的动态平衡

**背景**: 总QPS=800，配置 Unity=60%, IronSource=40%

**维度数据**:
- **Unity**: targetQps=480, reqQps=320, sspQps=700, 填充率=92%
- **IronSource**: targetQps=320, reqQps=480, sspQps=500, 填充率=70%

**按reqQps逻辑分析**:
```java
Unity: reqQps(320) < targetQps(480) → gap = 160 (严重不足)
IronSource: reqQps(480) > targetQps(320) → 超额 = 160 (过度分配)
重分配: IronSource减少160给Unity，实现60:40的配置比例
```

**按sspQps逻辑分析**:
```java
Unity: sspQps(700) > targetQps(480) → 无gap
IronSource: sspQps(500) > targetQps(320) → 无gap
结果: Unity只得到40%的实际分配，违背了60:40的配置意图
```

**结论**: 运营人员配置60:40的比例是有业务意图的，Unity的高填充率应该获得更多流量。reqQps逻辑能确保配置意图得到执行。

### 案例4：系统收敛性验证

**背景**: 验证多轮调控后系统是否能收敛到稳定状态

**初始状态**:
- **维度A**: targetQps=200, reqQps=120, sspQps=350
- **维度B**: targetQps=200, reqQps=280, sspQps=320

**reqQps逻辑的多轮调控**:
```java
第1轮: A gap=80, B超额=80 → B给A 80
第2轮: A=200, B=200 → 达到平衡，系统稳定
第3轮: 无需调整，系统收敛
```

**sspQps逻辑的多轮调控**:
```java
第1轮: A无gap, B无gap → 无调整
第2轮: A=120, B=280 → 持续不平衡
第N轮: 永远无法达到配置目标
```

**结论**: reqQps逻辑能让系统快速收敛到配置目标，而sspQps逻辑导致系统失去调控能力。

## 业务逻辑合理性分析

### 1. 配置目标的实现
- **targetQps是运营人员的业务决策**，代表每个维度应该获得的流量分配
- **reqQps反映当前分配状态**，如果< targetQps说明分配不足，需要调整
- **sspQps只是供给上限**，不代表当前分配是否合理

### 2. 流量价值最大化
- 高填充率的维度应该获得更多流量分配
- 如果高价值维度的reqQps < targetQps，说明存在优化空间
- 通过重分配可以提升整体收益

### 3. 系统动态平衡
- reqQps逻辑形成负反馈机制：不足的维度获得补充，超额的维度被调减
- 最终所有维度都能接近targetQps，实现动态平衡
- sspQps逻辑缺乏这种自我调节机制

## 对需求文档的重新理解

需求文档中提到的"供给不足"应该理解为"分配不足"而不是"流量供给不足"：

- **"实际供给无法满足配置的目标"** → 实际分配(reqQps)无法满足配置目标(targetQps)
- **"闲置的QPS指标"** → 超过目标分配的QPS，可以重新分配给不足的维度
- **"有充足流量"** → 有充足的分配余量，可以接收重分配的QPS

## 结论

基于以上分析，QPS缺口计算应该使用 `reqQps < targetQps` 的逻辑，理由如下：

1. **实现配置目标**: 确保每个维度都能按照运营配置获得合理分配
2. **优化流量价值**: 让高填充率维度获得更多流量，提升整体收益  
3. **系统稳定性**: 形成有效的负反馈机制，快速收敛到平衡状态
4. **业务合理性**: 符合流量分配公平性和价值最大化的业务目标

当前代码的实现逻辑是正确的，应该继续使用 `reqQps < targetQps` 来计算QPS缺口。
