apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: traffic-server
  namespace: kube-adx
  labels:
    # app: traffic-server-canary
    app: traffic-server
    prduct: "true"
spec:
  minReadySeconds: 180
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      # app: traffic-server-canary
      app: traffic-server
      prduct: "true"
  template:
    metadata:
      labels:
        # app: traffic-server-canary
        app: traffic-server
        prduct: "true"
    spec:
      # securityContext:
      #   sysctls:
      #   - name: net.core.somaxconn
      #     value: "20480"
      #   - name: net.core.wmem_default
      #     value: "262144"
      #   - name: net.core.wmem_max
      #     value: "262144"
      #   - name: net.core.tcp_wmem
      #     value: "262144 262144 262144"

      # hostNetwork: true
      imagePullSecrets:
        - name: aliyun-enterprise-docker-token

      # 使用configmap挂载的方式代替
      # configmap挂载的方式
      initContainers:
      - command:  ["/bin/sh", "-c", "sysctl -w net.core.somaxconn=20480"]
        image: busybox:latest
        imagePullPolicy: IfNotPresent
        name: init-c
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log

      containers:
      - name: traffic-server-canary
        image: aliyuncs.com/dsp-register/traffic-server:v0.0
        imagePullPolicy: IfNotPresent
        env:
          - name: TZ
            value: Asia/Shanghai
          - name: aliyun_logs_traffic-server-stdout
            value: stdout
          - name: aliyun_logs_traffic-server
            value: /traffic-server/logs/*.log
          - name: aliyun_logs_traffic-server_tags
            value: app=traffic-server
          - name: aliyun_logs_traffic-server-stdout_ttl
            value: "15"
          - name: NODE_IP
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
        ports:
        - containerPort: 9999
          hostPort: 9999
          protocol: TCP
          name: http
        resources:
          requests: # 必须设置，不然HPA无法运行。
            cpu: "4"
        #     memory: "8Gi"

        # readinessProbe:
        #   failureThreshold: 3
        #   tcpSocket:
        #     port: 8670
        #   initialDelaySeconds: 15
        #   periodSeconds: 3
        #   successThreshold: 1
        #   timeoutSeconds: 2
        # livenessProbe:
        #   failureThreshold: 10
        #   tcpSocket:
        #     port: 8670
        #   initialDelaySeconds: 60
        #   periodSeconds: 3
        #   successThreshold: 1
        #   timeoutSeconds: 2

        volumeMounts:
          - name: tz-config
            mountPath: /etc/localtime
          - name: logs
            mountPath: /traffic-server/logs
      # dnsPolicy: Default
      tolerations: 
        - key: "node.kubernetes.io/network-unavailable"
          operator: "Exists"
          effect: "NoSchedule"
      volumes:
        - name: tz-config
          hostPath:
            path: /etc/localtime
        - name: logs
          hostPath:
            path: /home/<USER>/traffic-server/logs
      nodeSelector:
        dsp-traffic-server: "true"